import { HTMLAttributes, ReactNode } from 'react';
import { cn } from '@/utils/class-utils';

interface BoxProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  variant?: 'default' | 'elevated' | 'muted' | 'outline' | 'ghost';
}

export function Box({ children, variant = 'default', className, ...props }: BoxProps) {
  const baseClasses = 'rounded-xl p-4 transition-shadow';

  const variantClasses = {
    default: 'bg-white border border-gray-200 shadow-sm',
    elevated: 'bg-white border border-gray-200 shadow-md',
    muted: 'bg-gray-100 text-gray-500',
    outline: 'bg-white border border-blue-500',
    ghost: 'bg-transparent border-none',
  };

  return (
    <div className={cn(baseClasses, variantClasses[variant], className)} {...props}>
      {children}
    </div>
  );
}
