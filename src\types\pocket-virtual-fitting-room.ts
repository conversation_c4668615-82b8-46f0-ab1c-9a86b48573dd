import { GENDER_OPTIONS, NAVIGATION_OPTIONS } from '@/constants/pocket-virtual-fitting-room';

export type Gender = (typeof GENDER_OPTIONS)[keyof typeof GENDER_OPTIONS];

export type Navigation = (typeof NAVIGATION_OPTIONS)[keyof typeof NAVIGATION_OPTIONS];

export type SizeGuideKeys =
  | 'gender'
  | 'isMetric'
  | 'age'
  | 'weight'
  | 'height'
  | 'heightFt'
  | 'heightIn'
  | 'bodyShapeChest'
  | 'bodyShapeWaist'
  | 'bodyShapeHip'
  | 'shoeSize'
  | 'shoeWidth'
  | 'shoeShape'
  | 'chest'
  | 'waist'
  | 'hip'
  | 'composedMeasures'
  | 'clothingMeasures'
  | 'shoeMeasures';

export type SizeGuideValues =
  | 'gender'
  | 'isMetric'
  | 'clothingMeasures.age'
  | 'clothingMeasures.weight'
  | 'clothingMeasures.height'
  | 'clothingMeasures.heightFt'
  | 'clothingMeasures.heightIn'
  | 'clothingMeasures.composedMeasures.bodyShapeChest'
  | 'clothingMeasures.composedMeasures.bodyShapeWaist'
  | 'clothingMeasures.composedMeasures.bodyShapeHip'
  | 'clothingMeasures.composedMeasures.chest'
  | 'clothingMeasures.composedMeasures.waist'
  | 'clothingMeasures.composedMeasures.hip'
  | 'shoeMeasures.shoeSize'
  | 'shoeMeasures.shoeWidth'
  | 'shoeMeasures.shoeShape'
  | 'clothingMeasures.composedMeasures'
  | 'clothingMeasures'
  | 'shoeMeasures';

export interface PocketVFRFormData {
  gender: Gender | undefined;
  isMetric: boolean;
  clothingMeasures: ClothingMeasures;
  shoeMeasures?: ShoeMeasures;
}

export interface ShoeMeasures {
  shoeSize: string;
  shoeWidth: string;
  shoeShape: string;
}

export interface ClothingMeasures {
  height: string;
  heightIn?: string;
  heightFt?: string;
  weight: string;
  age: string;
  composedMeasures: ComposedMeasures;
}

export interface ComposedMeasures {
  chest: number;
  waist: number;
  hip: number;
  bodyShapeChest: number;
  bodyShapeWaist: number;
  bodyShapeHip: number;
}

export enum GenericStep {
  SELECT_CATEGORY = 0,
}

export enum ClothingStep {
  BODY_MEASURES = 1,
  BODY_SHAPE = 2,
}

export enum ShoeStep {
  SHOE_MEASURES = 3,
  SHOE_SHAPE = 4,
  SHOE_KNOWN_MEASURE = 5,
}

export type PocketVFRStep = GenericStep | ClothingStep | ShoeStep;
