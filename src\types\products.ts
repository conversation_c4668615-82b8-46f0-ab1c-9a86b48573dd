import { CatalogClothType, ProductCardType } from './carousel';

export interface Product {
  id: string;
  title: string;
  price: string;
  imageLink: string;
  link: string;
  productType: string;
  gender: 'female' | 'male' | 'unisex';
  availability: string;
  productHash: string;
  itemGroupId: string | number;
  brand: string;
  color: string;
  gtin: number | string;
  additionalImageLinks: Array<string>;
  clothType: CatalogClothType;
}

export interface SimilarProduct extends Product {
  pageNum?: number;
}

export interface ComplementaryProduct extends Product {
  suitableSizes?: SuitableSizes[];
  itemType?: ProductCardType;
}

export interface SuitableSizes {
  size: string;
  comfortable: number;
  value: number;
}

export interface PageDetails {
  page: number;
  perPage: number;
  total: number;
}

export interface ComplementaryProducts {
  baseProduct: ComplementaryProduct;
  complementary: {
    first?: ComplementaryProduct;
    second?: ComplementaryProduct;
  }[];
  invalidPersonaHash: boolean;
}

export type ComplementaryProductsBundle = Array<ComplementaryProduct[]>;

export interface SimilarProductsParams {
  tenantId: number;
  collectionName: string;
  sid: string;
  permalink: string;
  page?: number;
  perPage?: number;
  filterByWhatFitsMe?: boolean;
  personaHash?: string;
  sizeSystem?: string;
  currency: string;
  locale: string;
  similarityThreshold: number;
}

export interface ComplementaryProductsParams {
  tenantId: number;
  collectionName: string;
  sid: string;
  permalink: string;
  sizeSystem?: string;
  personaHash?: string;
  similarityThreshold: number;
  limit?: number;
  currency: string;
  locale: string;
  filterByWhatFitsMe?: boolean;
}

export interface SimilarProductsResponse {
  products: SimilarProduct[];
  pageDetails: PageDetails;
  invalidPersonaHash?: boolean;
}

export interface SimilarProductsApiResponse extends PageDetails {
  data: SimilarProduct[];
  invalidPersonaHash?: boolean;
}
