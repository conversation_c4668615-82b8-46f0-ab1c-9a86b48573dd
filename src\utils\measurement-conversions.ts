import { getSanitizedShape } from './normalize-shape';
import { MEASUREMENT_LIMITS } from '@/constants/pocket-virtual-fitting-room';
import { ClothingMeasures } from '@/types/pocket-virtual-fitting-room';

export const convertCmToFtIn = (cm: number): { ft: number; inches: number } => {
  if (cm > MEASUREMENT_LIMITS.convertibleRules.cm.max) {
    return { ft: MEASUREMENT_LIMITS.height.feet.max, inches: MEASUREMENT_LIMITS.height.inches.max };
  }
  if (cm < MEASUREMENT_LIMITS.convertibleRules.cm.min) {
    return { ft: MEASUREMENT_LIMITS.height.feet.min, inches: MEASUREMENT_LIMITS.convertibleRules.inches.min };
  }
  const totalInches = cm / 2.54;
  const ft = Math.floor(totalInches / 12);
  const inches = Math.round(totalInches - ft * 12);
  return { ft, inches };
};

export const convertFtInToCm = (ft: number, inches: number): number => {
  const totalInches = Math.round((ft * 12 + inches) * 2.54);
  return Math.max(totalInches, 1);
};

export const convertKgToLb = (kg: number): number => {
  if (kg > MEASUREMENT_LIMITS.convertibleRules.kg.max) {
    return MEASUREMENT_LIMITS.weight.lb.max;
  }
  return Math.round(kg * 2.20462);
};

export const convertLbToKg = (lb: number): number => {
  if (lb < MEASUREMENT_LIMITS.convertibleRules.lb.min) {
    return MEASUREMENT_LIMITS.weight.kg.min;
  }
  return Math.round(lb / 2.20462);
};

export function normalizeHeight(heightFt: string, heightIn: string) {
  const height = convertFtInToCm(Number(heightFt), Number(heightIn));
  return height;
}

export function normalizeWeight(weight: string, isMetric: boolean) {
  if (isMetric) {
    return weight;
  }
  return convertLbToKg(Number(weight)).toString();
}

export function normalizeMeasure(measure: number, limits: { min: number; max: number }) {
  return measure > limits.min && measure < limits.max ? measure.toString() : '';
}

export function normalizeClothingMeasures(clothingMeasures: ClothingMeasures, isMetric: boolean) {
  const { height, weight, age, composedMeasures } = clothingMeasures;
  const { bodyShapeChest, bodyShapeWaist, bodyShapeHip } = composedMeasures;

  return {
    height: normalizeMeasure(Number(height), MEASUREMENT_LIMITS.height.cm),
    age: normalizeMeasure(Number(age), MEASUREMENT_LIMITS.age),
    weight: isMetric
      ? normalizeMeasure(Number(weight), MEASUREMENT_LIMITS.weight.kg)
      : normalizeMeasure(Number(weight), MEASUREMENT_LIMITS.weight.lb),
    bodyShapeChest: getSanitizedShape(bodyShapeChest),
    bodyShapeWaist: getSanitizedShape(bodyShapeWaist),
    bodyShapeHip: getSanitizedShape(bodyShapeHip),
  };
}
