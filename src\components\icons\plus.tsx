export function PlusIcon({ className }: { className?: string }) {
  return (
    <div className={`relative w-10 h-10 ${className}`}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        className="absolute inset-0"
      >
        <circle cx="20" cy="20" r="19.5" fill="white" stroke="#272727" strokeWidth="1" />
      </svg>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="40"
        height="40"
        viewBox="0 0 40 40"
        fill="none"
        className="absolute inset-0"
      >
        <path d="M20 13V27M13 20H27" stroke="#272727" strokeWidth="1.5" strokeLinecap="round" />
      </svg>
    </div>
  );
}
