import type { ButtonHTMLAttributes } from 'react';

export type ButtonVariant = 'primary' | 'secondary' | 'classic' | 'blank';
export type ButtonSize = 'sm' | 'md' | 'lg';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  className?: string;
  isLoading?: boolean;
  showSpinner?: boolean;
  disabled?: boolean;
}
