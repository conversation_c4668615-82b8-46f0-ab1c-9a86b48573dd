import type { ButtonHTMLAttributes, HTMLAttributes } from 'react';

export type ToggleVariant = 'default' | 'outline' | 'ghost';
export type ToggleSize = 'sm' | 'md' | 'lg';

export interface ToggleVariantProps {
  /** The visual style variant of the toggle */
  variant?: ToggleVariant;
  /** The size variant of the toggle */
  size?: ToggleSize;
}

export interface ToggleGroupContextType extends ToggleVariantProps {
  /** Whether the entire toggle group is disabled */
  disabled?: boolean;
  /** The currently selected values */
  selectedValues: string[];
  /** Callback to handle value changes */
  onValueChange: (value: string) => void;
  /** Whether the toggle group should take full width */
  fullWidth?: boolean;
}

export interface ToggleGroupProps extends HTMLAttributes<HTMLDivElement>, ToggleVariantProps {
  /** Whether to allow single or multiple selection */
  type: 'single' | 'multiple';
  /** The default selected value(s) (uncontrolled) */
  value: string | string[];
  /** Callback when selection changes */
  onValueChange: (value: string | string[]) => void;
  /** Whether the entire toggle group is disabled */
  disabled?: boolean;
  /** Whether the toggle group should take full width */
  fullWidth?: boolean;
}

export interface ToggleGroupItemProps
  extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'value' | 'onChange' | 'type'> {
  /** The value of this toggle item */
  value: string;
  /** The visual style variant of the toggle */
  variant?: ToggleVariant;
  /** The size variant of the toggle */
  size?: ToggleSize;
  /** Whether the toggle item is disabled */
  disabled?: boolean;
  /** Whether the toggle item should take full width */
  fullWidth?: boolean;
}
