import type { Meta, StoryObj } from '@storybook/react';
import { Button } from './button';
import { GraduationCapIcon } from '../../icons';

const meta = {
  title: 'Atoms/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      description: 'The visual style of the button',
      options: ['primary', 'secondary', 'blank'],
      control: { type: 'radio' },
      table: {
        defaultValue: { summary: 'primary' },
      },
    },
    fullWidth: {
      description: 'Whether the button should take up the full width of its container',
      control: 'boolean',
    },
    children: {
      description: 'The content to display inside the button',
      control: 'text',
    },
    className: {
      description: 'Additional CSS classes to apply to the button',
      control: 'text',
    },
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary Button',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Button',
  },
};

export const Blank: Story = {
  args: {
    variant: 'blank',
    children: 'Blank Button',
  },
};

export const FullWidth: Story = {
  args: {
    variant: 'primary',
    fullWidth: true,
    children: 'Full Width Button',
  },
};

export const IconOnly: Story = {
  args: {
    variant: 'primary',
    children: <GraduationCapIcon />,
  },
};
