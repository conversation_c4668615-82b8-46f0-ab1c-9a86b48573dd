import { t } from 'i18next';
import { useCallback } from 'react';
import { useFormContext } from 'react-hook-form';
import { BodyShape } from './ui/body-shape';
import { BodyShapeControl } from './ui/body-shape-control';
import { useBodyShape } from './use-body-shape';
import { Button, Text } from '@/components/atoms';
import { Step } from '@/components/atoms/step';
import { SIZE_GUIDE_KEYS } from '@/constants/pocket-virtual-fitting-room';
import { syncedDetails, syncedStep } from '@/hooks/use-pocket-vfr-form';
import { getBodyDefs } from '@/services/profile-measurements';
import { useGlobalContext } from '@/store/global';
import {
  ClothingStep,
  Gender,
  GenericStep,
  PocketVFRFormData,
  SizeGuideKeys,
} from '@/types/pocket-virtual-fitting-room';
import { cn } from '@/utils/class-utils';

interface BodyShapeStepProps {
  isSmallerDevices: boolean;
}

const RANGE_MIN = 1;
const RANGE_MAX = 5;
const INCREMENT = 1;
const DECREMENT = -1;

const shapeSections: SizeGuideKeys[] = ['bodyShapeChest', 'bodyShapeWaist', 'bodyShapeHip'];

const BodyShapeStyles = {
  content: {
    container: {
      base: 'flex items-center gap-2',
      mobile: 'gap-1 p-0',
    },
    bodySelectores: {
      base: 'flex flex-col body-shape-height justify-center flex-shrink-0 lg:gap-4 gap-2',
    },
  },
  header: { text: 'text-center color-[#555] px-3 md900:px-0' },
};

export const BodyShapeStep = ({ isSmallerDevices }: BodyShapeStepProps) => {
  const { getValues, setValue, handleSubmit, watch, reset } = useFormContext<PocketVFRFormData>();

  const formValues = getValues();
  const { bodyImageUrl, setBodyImageUrl, bmi } = useBodyShape(formValues);

  const {
    actions: { setCurrentStep, setDetailsSaved },
    state: { detailsSaved },
  } = useGlobalContext();

  const handleBodyShapeChange = (shape: SizeGuideKeys, operationType: number) => {
    const fieldPath = SIZE_GUIDE_KEYS[shape];
    const currentValue = Number(getValues(fieldPath));
    const newValue = currentValue + operationType;
    if (newValue >= RANGE_MIN && newValue <= RANGE_MAX) {
      setValue(fieldPath, newValue);
      const { gender, clothingMeasures } = getValues();
      const { composedMeasures } = clothingMeasures || {};
      setBodyImageUrl(
        getBodyDefs({
          gender: gender as Gender,
          bmi,
          skinType: 0,
          bodyShapeChest: composedMeasures?.bodyShapeChest,
          bodyShapeWaist: composedMeasures?.bodyShapeWaist,
          bodyShapeHip: composedMeasures?.bodyShapeHip,
        })
      );
    }
  };

  const onPreviousStep = () => {
    syncedStep(ClothingStep.BODY_MEASURES, setCurrentStep);
    reset(detailsSaved);
  };

  const handleLimitReached = useCallback(
    (shape: SizeGuideKeys, operationType: number) => {
      const fieldPath = SIZE_GUIDE_KEYS[shape];
      const current = Number(watch(fieldPath));
      return operationType === INCREMENT ? current >= RANGE_MAX : current <= RANGE_MIN;
    },
    [watch]
  );

  const onSubmit = () => {
    syncedDetails(getValues(), setDetailsSaved);
    syncedStep(GenericStep.SELECT_CATEGORY, setCurrentStep);
  };

  return (
    <Step.Root className={cn({ 'py-1 gap-1 items-center': isSmallerDevices })}>
      <Step.Header>
        <Text size={isSmallerDevices ? 'md' : 'sm'} className={cn(BodyShapeStyles.header.text, 'fh-form__title')}>
          {t('pocket_vfr.clothes.body_shape.caption')}
        </Text>
      </Step.Header>
      <Step.Content
        className={cn(BodyShapeStyles.content.container.base, {
          [BodyShapeStyles.content.container.mobile]: isSmallerDevices,
        })}
      >
        <div className={BodyShapeStyles.content.bodySelectores.base}>
          {shapeSections.map((shape) => (
            <BodyShapeControl
              key={`minus-${shape}`}
              shape={shape}
              operationType={DECREMENT}
              isDisabled={handleLimitReached(shape, DECREMENT)}
              onClick={handleBodyShapeChange}
            />
          ))}
        </div>
        <BodyShape avatarSrc={bodyImageUrl} isMobile={isSmallerDevices} />
        <div className={BodyShapeStyles.content.bodySelectores.base}>
          {shapeSections.map((shape) => (
            <BodyShapeControl
              key={`plus-${shape}`}
              shape={shape}
              operationType={INCREMENT}
              isDisabled={handleLimitReached(shape, INCREMENT)}
              onClick={handleBodyShapeChange}
            />
          ))}
        </div>
      </Step.Content>
      <Step.Footer className="flex gap-2">
        <Button fullWidth variant="secondary" size="sm" onClick={onPreviousStep}>
          {t('pocket_vfr.action_button.go_back')}
        </Button>
        <Button fullWidth variant="primary" size="sm" onClick={handleSubmit(onSubmit)}>
          {t('pocket_vfr.action_button.save_details')}
        </Button>
      </Step.Footer>
    </Step.Root>
  );
};
