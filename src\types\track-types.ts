import { ComplementaryProduct, SimilarProduct } from './products';
import { TrackEvents } from '../constants/tracking-events';

type TrackEventErrorResponse = {
  error: string;
  message: string[];
};

export type TrackEventSuccessResponse = {
  message: string;
};

export type TrackEventResponse = TrackEventSuccessResponse | TrackEventErrorResponse;

export type TrackEventParams = {
  eventName: TrackEvents;
  product: SimilarProduct;
  recommendationSize?: string;
  personaHash?: string;
  baseProduct?: ComplementaryProduct;
  first?: ComplementaryProduct;
  second?: ComplementaryProduct;
  index?: number;
};

export interface TrackEventData {
  sid: string;
  tenantId: number;
  personaHash?: string;
  permalink?: string;
  properties: Record<string, unknown>;
}

export interface TrackEventPayload extends TrackEventData {
  eventName: TrackEvents;
}
