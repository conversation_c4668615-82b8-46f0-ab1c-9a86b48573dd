import { LazyLoadImage } from '@/components/atoms';
import { cn } from '@/utils/class-utils';

export function BodyShape({ avatarSrc, isMobile }: { avatarSrc: string; isMobile: boolean }) {
  return (
    <div className="relative flex items-center justify-center overflow-hidden body-shape-height w-[15.188rem]">
      <LazyLoadImage
        enableCropping={false}
        src={avatarSrc}
        alt="Body Shape"
        className={cn('object-cover md900:pt-18 body-shape-image-crop', {
          'translate-y-[6rem]': isMobile,
        })}
      />
      <div className="absolute bottom-0 w-full h-[8%] bg-gradient-to-t from-white/95 to-transparent backdrop-blur-sm pointer-events-none" />
    </div>
  );
}
