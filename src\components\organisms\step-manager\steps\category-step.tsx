import { t } from 'i18next';
import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { CategoryStepProps, StepType } from '../types';
import { Button, Step, Text } from '@/components/atoms';
import { ArrowIcon, CheckIcon, Clothing, Footwear } from '@/components/icons';
import { ContentBox } from '@/components/molecules/content-box';
import { SIZE_GUIDE_KEYS } from '@/constants/pocket-virtual-fitting-room';
import { deleteCategoryData, postMessageToParent, syncedStep } from '@/hooks';
import { useGlobalContext } from '@/store/global';
import {
  ClothingStep,
  Gender,
  PocketVFRFormData,
  PocketVFRStep,
  ShoeStep,
  SizeGuideKeys,
  SizeGuideValues,
} from '@/types/pocket-virtual-fitting-room';
import { cn } from '@/utils/class-utils';
import { convertLbToKg } from '@/utils/measurement-conversions';
import getNormalizedShape from '@/utils/normalize-shape';

const CategoryStepStyles = {
  root: {
    base: 'flex flex-col gap-2',
    mobile: 'gap-3',
  },
  header: {
    mobile: 'text-center',
  },
  content: {
    container: {
      base: 'flex flex-col gap-2',
      mobile: 'gap-4 mt-2',
    },
    categoryBox: {
      base: 'flex flex-col gap-2 p-3',
      mobile: 'gap-4',
      submitEnabled: 'relative rounded-[5px] border border-[#79CFA4;]',
    },
    buttons: {
      base: 'rounded-[0.250rem] h-7 pr-0 gap-0 md900:px-0',
      icon: 'grow flex justify-end',
      text: 'grow text-end',
    },
    arrowIcon: {
      positionRight: 'rotate-180',
      disabled: 'gray',
      default: 'black',
    },
  },
  footer: {
    container: {
      base: 'mt-auto',
      mobile: 'mt-2',
    },
    submitButton: {
      base: 'h-[37px] text-nowrap text-sm font-sans font-normal',
      mobile: 'h-[48px] text-base',
    },
  },
};

export const CategoryStep = ({ isSmallerDevices }: CategoryStepProps) => {
  const {
    actions: { setCurrentStep, setLoadingRecommendation, setPreviousSizeNotFound, setDetailsSaved },
  } = useGlobalContext();

  const { getValues, reset } = useFormContext<PocketVFRFormData>();
  const { gender, isMetric, clothingMeasures } = getValues();
  const { height, heightIn, heightFt, weight, age, composedMeasures } = clothingMeasures || {};
  const { bodyShapeChest, bodyShapeWaist, bodyShapeHip } = composedMeasures || {};

  const clothestDetail = useMemo(() => {
    const finalHeight = isMetric ? `${height}cm` : `${heightFt}'${heightIn}"`;
    const finalWeight = isMetric ? `${weight}kg` : `${weight}lbs`;
    const finalGender = gender === 'M' ? 'pocket_vfr.gender.male' : 'pocket_vfr.gender.female';

    if (!finalHeight || !finalWeight || !age || !gender) {
      return null;
    }

    return `${t(finalGender)} | ${finalHeight} | ${finalWeight} | ${age} years`;
  }, [height, heightFt, heightIn, isMetric, weight, age, gender]);

  const shoesDetail = null;

  const enableSubmit = useMemo(() => {
    return {
      clothing: clothestDetail !== null,
      shoes: shoesDetail !== null,
      some: clothestDetail !== null || shoesDetail !== null,
    };
  }, [clothestDetail]);

  const handleCategorySelect = (stepTo: PocketVFRStep) => {
    syncedStep(stepTo, setCurrentStep);
  };

  const handleDeleteProfile = (category: SizeGuideKeys) => {
    deleteCategoryData(reset, category, setDetailsSaved);
    postMessageToParent({ id: 'szb_reset_persona_hash' });
  };

  const getRecommendation = () => {
    setPreviousSizeNotFound(false);

    postMessageToParent({
      id: 'szb_get_recommended_size',
      data: {
        gender: gender as Gender,
        height: height as string,
        isMetric,
        weight: isMetric ? (weight as string) : convertLbToKg(Number(weight)).toString(),
        age: age as string,
        bodyShapeChest: getNormalizedShape(bodyShapeChest),
        bodyShapeWaist: getNormalizedShape(bodyShapeWaist),
        bodyShapeHip: getNormalizedShape(bodyShapeHip),
        chest: composedMeasures.chest,
        waist: composedMeasures.waist,
        hip: composedMeasures.hip,
      },
    });

    setLoadingRecommendation(true);
  };

  const ActionsFooterContent = ({ category }: { category: SizeGuideValues }) => {
    let stepKey: PocketVFRStep = ClothingStep.BODY_MEASURES;
    let categoryKey: StepType = StepType.CLOTHING;
    if (category === SIZE_GUIDE_KEYS.shoeMeasures) {
      stepKey = ShoeStep.SHOE_MEASURES;
      categoryKey = StepType.SHOES;
    }

    if (enableSubmit[categoryKey]) {
      return (
        <div className="flex gap-2">
          <Button
            type="button"
            fullWidth
            variant="classic"
            className={CategoryStepStyles.content.buttons.base}
            size="sm"
            onClick={() => handleDeleteProfile(category as SizeGuideKeys)}
          >
            {t('pocket_vfr.clothes.button.delete_profile')}
          </Button>
          <Button
            type="button"
            className={CategoryStepStyles.content.buttons.base}
            fullWidth
            variant="classic"
            size="sm"
            onClick={() => handleCategorySelect(stepKey)}
          >
            <Text as="span" weight="medium" size="md" className={CategoryStepStyles.content.buttons.text}>
              {t('pocket_vfr.clothes.button.edit_details')}
            </Text>
            <div id="arrow-icon-vfr" className={CategoryStepStyles.content.buttons.icon}>
              <ArrowIcon
                className={CategoryStepStyles.content.arrowIcon.positionRight}
                color={CategoryStepStyles.content.arrowIcon.default}
                size={19}
              />
            </div>
          </Button>
        </div>
      );
    }

    return (
      <Button
        fullWidth
        disabled={category === SIZE_GUIDE_KEYS.shoeMeasures}
        type="button"
        size="sm"
        variant="classic"
        className={CategoryStepStyles.content.buttons.base}
        onClick={() => handleCategorySelect(stepKey)}
      >
        <Text as="span" weight="medium" size="md" className={CategoryStepStyles.content.buttons.text}>
          {t('pocket_vfr.clothes.button.enter_details')}
        </Text>
        <div id="arrow-icon-vfr" className={CategoryStepStyles.content.buttons.icon}>
          <ArrowIcon
            className={CategoryStepStyles.content.arrowIcon.positionRight}
            color={CategoryStepStyles.content.arrowIcon.default}
            size={19}
          />
        </div>
      </Button>
    );
  };

  return (
    <Step.Root
      className={cn(CategoryStepStyles.root.base, {
        [CategoryStepStyles.root.mobile]: isSmallerDevices,
      })}
    >
      <Step.Header>
        <Text
          size={isSmallerDevices ? 'md' : 'sm'}
          className={cn('fh-form__title', { [CategoryStepStyles.header.mobile]: isSmallerDevices })}
        >
          {t('pocket_vfr.introduction.caption')}
        </Text>
      </Step.Header>
      <Step.Content
        className={cn(CategoryStepStyles.content.container.base, {
          [CategoryStepStyles.content.container.mobile]: isSmallerDevices,
        })}
      >
        <ContentBox.Root
          className={cn(CategoryStepStyles.content.categoryBox.base, {
            [CategoryStepStyles.content.categoryBox.mobile]: isSmallerDevices,
            [CategoryStepStyles.content.categoryBox.submitEnabled]: enableSubmit[StepType.CLOTHING],
          })}
        >
          {enableSubmit[StepType.CLOTHING] && (
            <CheckIcon className="absolute top-0 right-0 translate-x-1/2 -translate-y-1/2 z-10" />
          )}
          <ContentBox.Main className="inline-flex gap-3">
            <div className="flex items-center">
              <Clothing />
            </div>
            <div>
              <Text size="sm" weight="semibold">
                {t('pocket_vfr.cloth_type.clothes')}
              </Text>
              {enableSubmit[StepType.CLOTHING] ? (
                <Text size="sm">{clothestDetail}</Text>
              ) : (
                <Text size="sm">{t('pocket_vfr.clothes.title')}</Text>
              )}
            </div>
          </ContentBox.Main>
          <ContentBox.Footer>
            <ActionsFooterContent category={SIZE_GUIDE_KEYS.clothingMeasures} />
          </ContentBox.Footer>
        </ContentBox.Root>
        <ContentBox.Root
          className={cn(CategoryStepStyles.content.categoryBox.base, {
            [CategoryStepStyles.content.categoryBox.mobile]: isSmallerDevices,
          })}
        >
          {enableSubmit[StepType.SHOES] && (
            <CheckIcon className="absolute top-0 right-0 translate-x-1/2 -translate-y-1/2 z-10" />
          )}
          <ContentBox.Main className="inline-flex gap-3">
            <div className="flex items-center">
              <Footwear />
            </div>
            <div>
              <Text size="sm" weight="semibold">
                {t('pocket_vfr.cloth_type.shoe')}
              </Text>
              <Text size="sm">{t('pocket_vfr.shoe.title')}</Text>
            </div>
          </ContentBox.Main>
          <ContentBox.Footer>
            <ActionsFooterContent category={SIZE_GUIDE_KEYS.shoeMeasures} />
          </ContentBox.Footer>
        </ContentBox.Root>
      </Step.Content>
      <Step.Footer
        className={cn(CategoryStepStyles.footer.container.base, {
          [CategoryStepStyles.footer.container.mobile]: isSmallerDevices,
        })}
      >
        <Button
          type="button"
          variant="primary"
          onClick={getRecommendation}
          fullWidth
          className={cn(CategoryStepStyles.footer.submitButton.base, 'fh-form__submit__button', {
            [CategoryStepStyles.footer.submitButton.mobile]: isSmallerDevices,
          })}
          disabled={!enableSubmit['some']}
          size="md"
        >
          {t('pocket_vfr.get_recommendation')}
        </Button>
      </Step.Footer>
    </Step.Root>
  );
};
