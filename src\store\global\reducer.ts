import { FORM_INITIAL_VALUES, FORM_STORAGE_KEY, STEP_STORAGE_KEY } from '@/constants/pocket-virtual-fitting-room';
import { GenericStep, PocketVFRFormData, PocketVFRStep } from '@/types/pocket-virtual-fitting-room';
import { GlobalState } from './types';

const storedStep =
  typeof window !== 'undefined' ? Number(localStorage.getItem(STEP_STORAGE_KEY)) : GenericStep.SELECT_CATEGORY;

const storedFormData = localStorage.getItem(FORM_STORAGE_KEY);
const { gender, clothingMeasures, isMetric } = storedFormData ? JSON.parse(storedFormData) : {};
const { bodyShapeChest, bodyShapeWaist, bodyShapeHip } = clothingMeasures || {};
console.log('storedFormData', storedFormData);
const storedDetails = storedFormData
  ? {
      isMetric,
      gender,
      clothingMeasures: {
        bodyShapeChest: Number(bodyShapeChest),
        bodyShapeWaist: Number(bodyShapeWaist),
        bodyShapeHip: Number(bodyShapeHip),
        ...clothingMeasures,
      },
    }
  : FORM_INITIAL_VALUES;

export const initialState: GlobalState = {
  sessionId: '',
  personaHash: null,
  errorCreatingProfile: false,
  previousSizeNotFound: false,
  currentStep: storedStep,
  loadingRecommendation: false,
  detailsSaved: storedDetails,
};

export enum GlobalActionTypes {
  GET_SESSION_INFO = 'GET_SESSION_INFO',
  SET_ERROR_PROFILE = 'SET_ERROR_PROFILE',
  SET_PREVIOUS_SIZE_NOT_FOUND = 'SET_PREVIOUS_SIZE_NOT_FOUND',
  SET_PERSONA_HASH = 'SET_PERSONA_HASH',
  SET_CURRENT_STEP = 'SET_CURRENT_STEP',
  SET_LOADING_RECOMMENDATION = 'SET_LOADING_RECOMMENDATION',
  SET_DETAILS_SAVED = 'SET_DETAILS_SAVED',
}

type SessionAction =
  | {
      kind: GlobalActionTypes.GET_SESSION_INFO;
      payload: {
        sessionId: string;
        personaHash: string;
      };
    }
  | { kind: GlobalActionTypes.SET_ERROR_PROFILE; payload: boolean | null }
  | { kind: GlobalActionTypes.SET_PREVIOUS_SIZE_NOT_FOUND; payload: boolean }
  | { kind: GlobalActionTypes.SET_PERSONA_HASH; payload: string | null }
  | { kind: GlobalActionTypes.SET_CURRENT_STEP; payload: PocketVFRStep }
  | { kind: GlobalActionTypes.SET_LOADING_RECOMMENDATION; payload: boolean }
  | { kind: GlobalActionTypes.SET_DETAILS_SAVED; payload: PocketVFRFormData };

export function sessionReducer(state: GlobalState, action: SessionAction): GlobalState {
  switch (action.kind) {
    case GlobalActionTypes.GET_SESSION_INFO:
      return {
        ...state,
        sessionId: action.payload.sessionId,
        personaHash: action.payload.personaHash,
      };
    case GlobalActionTypes.SET_ERROR_PROFILE:
      return {
        ...state,
        errorCreatingProfile: action.payload,
      };
    case GlobalActionTypes.SET_PREVIOUS_SIZE_NOT_FOUND:
      return {
        ...state,
        previousSizeNotFound: action.payload,
      };
    case GlobalActionTypes.SET_PERSONA_HASH:
      return {
        ...state,
        personaHash: action.payload,
      };
    case GlobalActionTypes.SET_CURRENT_STEP:
      return {
        ...state,
        currentStep: action.payload,
      };
    case GlobalActionTypes.SET_LOADING_RECOMMENDATION:
      return {
        ...state,
        loadingRecommendation: action.payload,
      };
    case GlobalActionTypes.SET_DETAILS_SAVED:
      return {
        ...state,
        detailsSaved: action.payload,
      };
    default:
      return state;
  }
}
