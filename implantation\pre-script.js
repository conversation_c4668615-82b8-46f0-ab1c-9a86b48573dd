window.SizebayFashionHint = (() => {
  return {
    getTenantId: () => {
      return 6273;
    },

    getCollectionName: () => {
      return 'shopify-sizebay-develop';
    },

    getPermalink: () => {
      return 'https://32c6c6-a9.myshopify.com/products/renner-female-bottom-short-curto-jeans-com-bordado-floral-e-barra-desfiada-azul';
      //return 'https://32c6c6-a9.myshopify.com/products/guess-male-tops-boys-bear-t-shirt-in-white';
    },

    getLang: () => {
      return 'en';
    },

    getPerPage: () => {
      return 8;
    },

    getBaseVrfUrl() {
      //return 'https://vfr-v3-production.sizebay.technology/';
      return 'https://vfr-v3-staging.sizebay.eu/';
    },

    getBaseFashionHintUrl() {
      // return 'https://fashion-hint.internalsizebay.com/';
      // return 'http://************:3000/'
      return 'http://localhost:3001/';
    },

    getSizeSystem() {
      return 'US';
    },

    getEnvironment() {
      return 'local';
    },

    getCurrency() {
      return 'USD';
    },

    getDefaultUnit() {
      return 'in';
    },

    getSimilarContainerId() {
      return 'fashion-hint-similar';
    },

    getComplementaryContainerId() {
      return 'fashion-hint-complementary';
    },

    getSimilarityThreshold() {
      return 0.1;
    },
  };
})();

const insertScript = (src, base) => {
  const app = document.createElement('script');
  app.id = base ? 'szb-vfr__base' : 'szb-vfr__module';
  app.setAttribute('src', src);
  document.querySelector('head').appendChild(app);
};

const sizebayImplantation = () => {
  const environment = window.SizebayFashionHint.getEnvironment();
  const baseFashionHintUrl = window.SizebayFashionHint.getBaseFashionHintUrl();

  if (environment === 'local') {
    return insertScript('http://localhost:5500/implantation/index.js', true);
  }

  return insertScript(`${baseFashionHintUrl}implantation/index.js`, true);
};

function SizebayInit() {
  sizebayImplantation();
}

SizebayInit();