import { DependencyList, useCallback, useEffect } from 'react';
import { PostMessageToParent } from '@/types/post-messages';

type MessageHandler = (event: MessageEvent) => void;

export function usePostMessage(handlers: Record<string, MessageHandler>, deps: DependencyList = []) {
  const handleMessage = useCallback(
    (event: MessageEvent) => {
      const messageId = event.data?.id;
      if (messageId && handlers[messageId]) {
        handlers[messageId](event);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    deps
  );

  useEffect(() => {
    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [handleMessage]);
}

export function postMessageToParent(message: PostMessageToParent) {
  window.parent.postMessage(message, '*');
}
