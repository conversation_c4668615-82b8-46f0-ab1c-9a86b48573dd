enum SanitizedShape {
  Loose = 5,
  <PERSON><PERSON><PERSON> = 4,
  Normal = 3,
  Tight = 2,
  Tighter = 1,
}

/**
 * Normalizes shape values in order to respect
 * <PERSON><PERSON><PERSON>'s algorithm.
 *
 * @param shape
 */
const getNormalizedShape = (shape: number) => {
  switch (shape) {
    case 5:
      return 2;
    case 4:
      return 1;
    case 3:
      return 0;
    case 2:
      return -1;
    case 1:
      return -2;
    default:
      return 0;
  }
};

/**
 * getNormalizedShape but reversed.
 *
 * @param sessionShape
 */
export const getSanitizedShape = (sessionShape: number) => {
  switch (sessionShape) {
    case 2:
      return SanitizedShape.Loose;
    case 1:
      return SanitizedShape.Slighter;
    case 0:
      return SanitizedShape.Normal;
    case -1:
      return SanitizedShape.Tight;
    case -2:
      return SanitizedShape.Tighter;
    default:
      return SanitizedShape.Normal;
  }
};

export default getNormalizedShape;
