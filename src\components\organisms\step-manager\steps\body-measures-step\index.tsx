import { t } from 'i18next';
import { ChangeEvent, useCallback, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { syncHeightUnits, syncWeightUnits } from './body-measures-utils';
import { Button, Text, ToggleGroup, ToggleGroupItem } from '@/components/atoms';
import { Step } from '@/components/atoms/step';
import { FormInput } from '@/components/molecules';
import { GENDER_OPTIONS, SIZE_GUIDE_KEYS } from '@/constants/pocket-virtual-fitting-room';
import { syncedStep } from '@/hooks/use-pocket-vfr-form';
import { useGlobalContext } from '@/store/global';
import {
  ClothingStep,
  Gender,
  GenericStep,
  PocketVFRFormData,
  SizeGuideValues,
} from '@/types/pocket-virtual-fitting-room';
import { cn } from '@/utils/class-utils';

interface BodyMeasuresStepProps {
  isSmallerDevices: boolean;
}

const BodyMeasuresStyles = {
  root: {
    base: 'flex flex-col gap-4',
    mobile: 'gap-4',
  },
  content: {
    container: {
      base: 'flex flex-col gap-2',
      mobile: 'gap-2',
    },
  },
  toggle: {
    container: 'gap-2',
    item: {
      base: 'text-[#272727] font-normal',
      selected: 'font-semibold',
    },
  },
};

export const BodyMeasuresStep = ({ isSmallerDevices }: BodyMeasuresStepProps) => {
  const {
    actions: { setCurrentStep },
    state: { detailsSaved },
  } = useGlobalContext();

  const { setValue, watch, reset } = useFormContext<PocketVFRFormData>();

  const { isMetric, clothingMeasures, gender } = watch();
  const { height, heightIn, heightFt, weight, age } = clothingMeasures || {};

  const handleInputChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      let name = e.target.name as SizeGuideValues;
      if (name !== SIZE_GUIDE_KEYS.gender && name !== SIZE_GUIDE_KEYS.isMetric) {
        name = `clothingMeasures.${name}` as SizeGuideValues;
      }
      setValue(name, e.target.value);
    },
    [setValue]
  );

  const onPreviousStep = () => {
    syncedStep(GenericStep.SELECT_CATEGORY, setCurrentStep);
    reset(detailsSaved);
  };

  const onNextStep = () => {
    const { clothingMeasures } = watch();
    const { age } = clothingMeasures || {};
    const nextStep = Number(age) > 11 ? ClothingStep.BODY_SHAPE : GenericStep.SELECT_CATEGORY;
    syncedStep(nextStep, setCurrentStep);
  };

  const enableNextButton = useMemo(() => {
    return (height || (heightFt && heightIn)) && weight && age;
  }, [height, heightFt, heightIn, weight, age]);

  const handleUnitSystemChange = useCallback(() => {
    setValue(SIZE_GUIDE_KEYS.isMetric, !isMetric);
    syncWeightUnits({ setValue, isPounds: !isMetric, weight: weight as string });
    syncHeightUnits({
      setValue,
      isMetric: !isMetric,
      fields: { height, heightIn, heightFt },
    });
  }, [setValue, isMetric, height, heightFt, heightIn, weight]);

  return (
    <Step.Root className={cn(BodyMeasuresStyles.root.base)}>
      <Step.Header className={cn('flex flex-col', { 'gap-3': isSmallerDevices })}>
        {isSmallerDevices && (
          <Text size="md" weight="medium" className={'fh-form__title text-center'}>
            {t('pocket_vfr.introduction.caption')}
          </Text>
        )}
        <ToggleGroup
          type="single"
          onValueChange={(value) => setValue('gender', value as Gender)}
          value={gender as Gender}
          className="gap-2"
        >
          <ToggleGroupItem
            value={GENDER_OPTIONS.FEMALE}
            className={cn(BodyMeasuresStyles.toggle.item.base, {
              [BodyMeasuresStyles.toggle.item.selected]: gender === GENDER_OPTIONS.FEMALE,
            })}
            fullWidth
          >
            {t('pocket_vfr.gender.female')}
          </ToggleGroupItem>
          <ToggleGroupItem
            value={GENDER_OPTIONS.MALE}
            className={cn(BodyMeasuresStyles.toggle.item.base, {
              [BodyMeasuresStyles.toggle.item.selected]: gender === GENDER_OPTIONS.MALE,
            })}
            fullWidth
          >
            {t('pocket_vfr.gender.male')}
          </ToggleGroupItem>
        </ToggleGroup>
      </Step.Header>
      <Step.Content className={cn(BodyMeasuresStyles.content.container.base)}>
        <FormInput
          label={t('pocket_vfr.measures.height')}
          type="height"
          defaultValue={isMetric ? height || '' : heightFt || ''}
          secondaryValue={heightIn}
          isMetric={isMetric}
          handleChange={handleInputChange}
          onToggleChange={handleUnitSystemChange}
          metricUnit="cm"
          imperialUnit="in"
        />
        <FormInput
          label={t('pocket_vfr.measures.weight')}
          type="weight"
          defaultValue={weight as string}
          isMetric={isMetric}
          handleChange={handleInputChange}
          onToggleChange={handleUnitSystemChange}
          metricUnit="kg"
          imperialUnit="lb"
        />
        <FormInput
          label={t('pocket_vfr.measures.age')}
          type="age"
          defaultValue={age as string}
          helperText={t('pocket_vfr.measures.years')}
          handleChange={handleInputChange}
        />
      </Step.Content>
      <Step.Footer>
        <Button fullWidth variant="secondary" size="sm" onClick={onPreviousStep}>
          {t('pocket_vfr.action_button.go_back')}
        </Button>
        <Button variant="primary" disabled={!enableNextButton} fullWidth size="sm" onClick={onNextStep}>
          {t('pocket_vfr.action_button.next')}
        </Button>
      </Step.Footer>
    </Step.Root>
  );
};
