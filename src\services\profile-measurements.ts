import { Gender } from '@/types/pocket-virtual-fitting-room';

export interface BodyProps {
  gender: Gender;
  bmi: string;
  skinType: number;
  bodyShapeChest: number;
  bodyShapeWaist: number;
  bodyShapeHip: number;
}

const BUCKET_URL = import.meta.env.VITE_S3_URL || '';
const BODY_SHAPE_URL = `${BUCKET_URL}assets/shapes/v4/new`;

export const getBodyDefs = (bodyProps: BodyProps): string => {
  const { gender, skinType, bmi, bodyShapeChest, bodyShapeWaist, bodyShapeHip } = bodyProps;
  return `${BODY_SHAPE_URL}/${skinType}/${gender}/toggle-off/${bmi}/0${bodyShapeChest}0${bodyShapeWaist}0${bodyShapeHip}.jpg`;
};
