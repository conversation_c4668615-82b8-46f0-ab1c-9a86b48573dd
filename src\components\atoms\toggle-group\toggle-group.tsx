import { createContext, useContext, useEffect, useState } from 'react';
import {
  toggleGroupBaseStyles,
  toggleGroupFullWidthStyles,
  toggleGroupSizeStyles,
  toggleItemBaseStyles,
  toggleItemFullWidthStyles,
  toggleItemSizeStyles,
} from './toggle-group.styles';
import type { ToggleGroupContextType, ToggleGroupItemProps, ToggleGroupProps } from './toggle-group.types';
import { cn } from '../../../utils/class-utils';

const ToggleGroupContext = createContext<ToggleGroupContextType | undefined>(undefined);

export function ToggleGroup({
  className,
  variant,
  size = 'md',
  children,
  type = 'single',
  value,
  onValueChange,
  disabled = false,
  fullWidth = false,
  ...props
}: ToggleGroupProps) {
  const [selectedValues, setSelectedValues] = useState<string[]>([]);

  useEffect(() => {
    if (value !== undefined) {
      setSelectedValues(Array.isArray(value) ? value : [value]);
    }
  }, [value]);

  const handleValueChange = (itemValue: string) => {
    if (disabled) return;

    let newValues: string[];
    if (type === 'single') {
      newValues = [itemValue];
    } else {
      newValues = selectedValues.includes(itemValue)
        ? selectedValues.filter((v) => v !== itemValue)
        : [...selectedValues, itemValue];
    }

    setSelectedValues(newValues);
    onValueChange?.(type === 'single' ? newValues[0] : newValues);
  };

  return (
    <ToggleGroupContext.Provider
      value={{
        variant,
        size,
        disabled,
        selectedValues,
        onValueChange: handleValueChange,
        fullWidth,
      }}
    >
      <div
        className={cn(
          toggleGroupBaseStyles,
          toggleGroupSizeStyles[size],
          fullWidth && toggleGroupFullWidthStyles,
          className
        )}
        role="group"
        {...props}
      >
        {children}
      </div>
    </ToggleGroupContext.Provider>
  );
}

export function ToggleGroupItem({
  className,
  value,
  children,
  size: itemSize,
  fullWidth: itemFullWidth,
  ...props
}: ToggleGroupItemProps) {
  const context = useContext(ToggleGroupContext);
  if (!context) {
    throw new Error('ToggleGroupItem must be used within a ToggleGroup');
  }

  const { selectedValues, onValueChange, size = 'md', fullWidth: groupFullWidth } = context;
  const isSelected = selectedValues.includes(value);
  const finalSize = itemSize || size;
  const shouldBeFullWidth = itemFullWidth ?? groupFullWidth;

  return (
    <button
      type="button"
      className={cn(
        toggleItemBaseStyles,
        toggleItemSizeStyles[finalSize],
        shouldBeFullWidth && toggleItemFullWidthStyles,
        'fh-toggle__group__item',
        className
      )}
      onClick={() => onValueChange(value)}
      disabled={isSelected}
      data-state={isSelected ? 'on' : 'off'}
      aria-pressed={isSelected}
      {...props}
    >
      {children}
    </button>
  );
}
