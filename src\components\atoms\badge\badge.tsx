import { useDevice } from '@/hooks';
import { DeviceType } from '@/types/devices';
import { cn } from '@/utils/class-utils';

interface BadgeProps {
  className?: string;
  children: React.ReactNode;
  variant: 'default';
  size: 'sm' | 'md' | 'lg';
}

const BadgeStyles = {
  base: 'flex justify-center items-center gap-2 rounded-full w-full h-9',
  mobile: 'h-7',
  variants: {
    size: {
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base',
    },
    color: {
      default: 'bg-[#F5F5F5] text-[#000]',
    },
  },
};

export function Badge({ className, children, variant, size }: BadgeProps) {
  return (
    <span
      className={cn(
        BadgeStyles.base,
        BadgeStyles.variants.size[size],
        BadgeStyles.variants.color[variant],
        { [BadgeStyles.mobile]: useDevice(DeviceType.Mobile) as boolean },
        className
      )}
    >
      {children}
    </span>
  );
}
