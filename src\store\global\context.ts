import { createContext } from 'react';
import { initialState } from './reducer';
import { GlobalContextData } from './types';

export const GlobalContext = createContext<GlobalContextData>({
  state: initialState,
  actions: {
    setErrorProfile: () => {},
    setPersonaHash: () => {},
    setPreviousSizeNotFound: () => {},
    setCurrentStep: () => {},
    setLoadingRecommendation: () => {},
    setDetailsSaved: () => {},
  },
});

GlobalContext.displayName = 'GlobalContext';
