import { useFormContext } from 'react-hook-form';
import { Carousel } from '@/components/organisms';
import { useLocalStorage, useSimilarProducts } from '@/hooks';
import { useGlobalContext } from '@/store/global';
import { CarouselVariant } from '@/types/carousel';
import { PocketVFRFormData } from '@/types/pocket-virtual-fitting-room';
import { useItemsPerPage } from '@/utils/items-per-page-utils';

export function SimilarProductsPage() {
  const { fetchNextPage, hasNextPage, isFetchingNextPage, products, isLoading } = useSimilarProducts();
  const { reset } = useFormContext<PocketVFRFormData>();
  const itemsPerPage = useItemsPerPage({
    itemsType: CarouselVariant.Similar,
  });
  const {
    state: { personaHash },
    actions: { setDetailsSaved },
  } = useGlobalContext();

  useLocalStorage(reset, setDetailsSaved);

  return (
    <Carousel
      itemsPerPage={itemsPerPage}
      products={products}
      isLoading={isLoading}
      onLoadMore={() => hasNextPage && fetchNextPage()}
      isFetchingMore={isFetchingNextPage}
      type={CarouselVariant.Similar}
      className={'fh-similar'}
      key={personaHash}
      hasNextPage={hasNextPage}
    />
  );
}
