import { useTranslation } from 'react-i18next';
import { Button, Text } from '../../atoms';
import { useDevice } from '@/hooks';
import { DeviceType } from '@/types/devices';
import { cn } from '@/utils/class-utils';

const InfoCardStyles = {
  container: {
    base: 'flex flex-col justify-center gap-4 p-3',
    desktop: 'h-max-[22.5rem] carousel-card-height',
    mobile: 'h-full p-3',
  },
  textContainer: {
    base: 'flex items-end h-[45%] text-center',
    mobile: 'px-3 h-2/5',
  },
  button: {
    container: 'flex flex-col h-[55%] gap-2',
    base: 'w-full',
    mobile: 'h-3/5',
  },
};

interface InfoCardProps {
  textInfo: string;
  btnInteractionText: string;
  onClose?: () => void;
  onAction: () => void;
}

export function InfoCard({ textInfo, btnInteractionText, onClose, onAction }: InfoCardProps) {
  const device = useDevice() as DeviceType;
  const isSmallerDevices = [DeviceType.SmallTablet, DeviceType.Mobile].includes(device);
  const { t } = useTranslation();

  return (
    <div
      className={cn(InfoCardStyles.container.base, {
        [InfoCardStyles.container.mobile]: isSmallerDevices,
        [InfoCardStyles.container.desktop]: !isSmallerDevices,
      })}
    >
      <div
        className={cn(InfoCardStyles.textContainer.base, { [InfoCardStyles.textContainer.mobile]: isSmallerDevices })}
      >
        <Text size={isSmallerDevices ? 'md' : 'sm'} color="gray">
          {textInfo}
        </Text>
      </div>
      <div className={cn(InfoCardStyles.button.container, { [InfoCardStyles.button.mobile]: isSmallerDevices })}>
        <Button
          variant="primary"
          size={isSmallerDevices ? 'md' : 'sm'}
          className={InfoCardStyles.button.base}
          onClick={onAction}
        >
          <Text size="sm" color="white">
            {btnInteractionText}
          </Text>
        </Button>
        {onClose ? (
          <Button
            variant="classic"
            size={isSmallerDevices ? 'md' : 'sm'}
            className={InfoCardStyles.button.base}
            onClick={onClose}
          >
            <Text size="sm">{t('generics.close')}</Text>
          </Button>
        ) : null}
      </div>
    </div>
  );
}
