import { memo, useEffect, useRef, useState } from 'react';
import { autoCropImage } from './lazy-load-image-utils';
import { CircularLoading } from '../circular-loading';
import { cn } from '@/utils/class-utils';

interface LazyLoadImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  enableCropping?: boolean;
  className?: string;
  fallbackSrc?: string;
  loadingClassName?: string;
}

const CircularLoadingStyles = {
  container: 'relative w-full aspect-[4/3]',
  overlay: 'absolute inset-0 flex items-center justify-center backdrop-blur-lg bg-white rounded-lg',
};

export const LazyLoadImage = memo(
  ({
    src,
    alt = 'image',
    className = '',
    loadingClassName = '',
    enableCropping = true,
    ...props
  }: LazyLoadImageProps) => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(false);
    const [processedSrc, setProcessedSrc] = useState<string | null>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      if (!src) return;

      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        if (enableCropping) {
          autoCropImage(img)
            .then((cropped) => {
              setProcessedSrc(cropped);
            })
            .catch(() => {
              setProcessedSrc(src);
            })
            .finally(() => {
              setLoading(false);
            });
        } else {
          setLoading(false);
        }
      };

      img.onerror = () => {
        setError(true);
        setLoading(false);
      };

      img.src = src;
    }, [src, enableCropping]);

    if (error) {
      return <img src={src} alt={alt} className={className} {...props} />;
    }

    if (loading) {
      return (
        <div className={cn(CircularLoadingStyles.container, loadingClassName)} ref={containerRef}>
          <div className={CircularLoadingStyles.overlay}>
            <CircularLoading />
          </div>
        </div>
      );
    }

    return <img src={processedSrc || src} alt={alt} className={className} {...props} />;
  }
);
