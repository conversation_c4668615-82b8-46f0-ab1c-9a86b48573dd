import { useEffect, useState } from 'react';
import { getBodyDefs } from '@/services/profile-measurements';
import { Gender, PocketVFRFormData } from '@/types/pocket-virtual-fitting-room';
import getBmi from '@/utils/get-bmi';
import { normalizeHeight, normalizeWeight } from '@/utils/measurement-conversions';

export const useBodyShape = (formValues: PocketVFRFormData) => {
  const [bmi, setBmi] = useState('');
  const [bodyImageUrl, setBodyImageUrl] = useState('');

  const { gender, isMetric, clothingMeasures } = formValues;
  const { height, weight, heightFt, heightIn, composedMeasures } = clothingMeasures || {};
  const { bodyShapeChest, bodyShapeWaist, bodyShapeHip } = composedMeasures || {};

  useEffect(() => {
    let normalizedHeight = height || '';
    let normalizedWeight = weight || '';

    if (!isMetric && heightFt && heightIn) {
      normalizedHeight = normalizeHeight(heightFt, heightIn).toString();
    }

    if (weight) {
      normalizedWeight = normalizeWeight(weight, isMetric);
    }

    setBmi(getBmi(normalizedWeight, normalizedHeight, gender as string));
  }, [height, heightFt, heightIn, weight, isMetric, gender]);

  useEffect(() => {
    if (!gender) return;

    const imageUrl = getBodyDefs({
      gender: gender as Gender,
      bmi,
      skinType: 0,
      bodyShapeChest: bodyShapeChest,
      bodyShapeWaist: bodyShapeWaist,
      bodyShapeHip: bodyShapeHip,
    });
    setBodyImageUrl(imageUrl);
  }, [bmi, gender, bodyShapeHip, bodyShapeWaist, bodyShapeChest]);

  return { bmi, bodyImageUrl, setBodyImageUrl };
};
