import { PocketVFRFormD<PERSON>, SizeGuideKeys, SizeGuideValues } from '@/types/pocket-virtual-fitting-room';

export const FORM_STORAGE_KEY = 'szb_pocket_vfr_form_data';

export const STEP_STORAGE_KEY = 'szb_current_step';

export const MEASUREMENT_LIMITS = {
  convertibleRules: {
    cm: {
      max: 241,
      min: 33,
    },
    inches: {
      min: 1,
    },
    kg: {
      max: 219,
    },
    lb: {
      min: 2,
    },
  },
  height: {
    feet: {
      min: 1,
      max: 7,
    },
    inches: {
      min: 0,
      max: 11,
    },
    cm: {
      min: 1,
      max: 300,
    },
  },
  weight: {
    kg: {
      min: 1,
      max: 300,
    },
    lb: {
      min: 1,
      max: 485,
    },
  },
  age: {
    min: 1,
    max: 120,
  },
} as const;

export const GENDER_OPTIONS = {
  FEMALE: 'F',
  MALE: 'M',
} as const;

export const SIZE_GUIDE_KEYS: Record<SizeGuideKeys, SizeGuideValues> = {
  gender: 'gender',
  isMetric: 'isMetric',
  age: 'clothingMeasures.age',
  weight: 'clothingMeasures.weight',
  height: 'clothingMeasures.height',
  heightFt: 'clothingMeasures.heightFt',
  heightIn: 'clothingMeasures.heightIn',
  bodyShapeChest: 'clothingMeasures.composedMeasures.bodyShapeChest',
  bodyShapeWaist: 'clothingMeasures.composedMeasures.bodyShapeWaist',
  bodyShapeHip: 'clothingMeasures.composedMeasures.bodyShapeHip',
  chest: 'clothingMeasures.composedMeasures.chest',
  waist: 'clothingMeasures.composedMeasures.waist',
  hip: 'clothingMeasures.composedMeasures.hip',
  shoeSize: 'shoeMeasures.shoeSize',
  shoeWidth: 'shoeMeasures.shoeWidth',
  shoeShape: 'shoeMeasures.shoeShape',
  composedMeasures: 'clothingMeasures.composedMeasures',
  clothingMeasures: 'clothingMeasures',
  shoeMeasures: 'shoeMeasures',
};

export const NAVIGATION_OPTIONS = {
  PREVIOUS: 'previous',
  NEXT: 'next',
  SAVE: 'save',
} as const;

export const TOGGLE_LABELS = {
  MALE: 'pocket_vfr.gender.male',
  FEMALE: 'pocket_vfr.gender.female',
  PREVIOUS: 'Go back',
  NEXT: 'Next',
  SAVE: 'Save details',
} as const;

export const FORM_INITIAL_VALUES: PocketVFRFormData = {
  gender: GENDER_OPTIONS.FEMALE,
  isMetric: true,
  clothingMeasures: {
    height: '',
    heightIn: '',
    heightFt: '',
    weight: '',
    age: '',
    composedMeasures: {
      chest: 0,
      waist: 0,
      hip: 0,
      bodyShapeChest: 3,
      bodyShapeWaist: 3,
      bodyShapeHip: 3,
    },
  },
};
