import { useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { Drawer, DrawerContent } from '@/components/molecules';
import { SizeRecommendationForm } from '@/components/organisms';
import { STEP_STORAGE_KEY } from '@/constants/pocket-virtual-fitting-room';
import { redirectOnClose, useLocalStorage } from '@/hooks';
import { postMessageToParent, usePostMessage } from '@/hooks/use-post-message';
import { useGlobalContext } from '@/store/global';
import { PocketVFRFormData } from '@/types/pocket-virtual-fitting-room';

export function DrawerOpenPage() {
  const [isOpen, setIsOpen] = useState(false);
  const { reset } = useFormContext<PocketVFRFormData>();
  const {
    state: { currentStep, detailsSaved },
    actions: { setDetailsSaved },
  } = useGlobalContext();

  useEffect(() => {
    const t = setTimeout(() => setIsOpen(true), 100);
    return () => clearTimeout(t);
  }, []);

  const stepToRedirect = useMemo(() => {
    const { clothingMeasures } = detailsSaved;
    const { age, height, weight } = clothingMeasures || {};
    const hasDetails = height && weight && age ? true : false;

    return redirectOnClose(currentStep, hasDetails);
  }, [currentStep, detailsSaved]);

  const handleClickOutside = () => {
    setIsOpen(false);
    localStorage.setItem(STEP_STORAGE_KEY, String(stepToRedirect));
    setTimeout(() => {
      postMessageToParent({
        id: 'szb_toggle_drawer',
      });
    }, 1000);
  };

  const handleClose = () => {
    setIsOpen(false);
    localStorage.setItem(STEP_STORAGE_KEY, String(stepToRedirect));
    postMessageToParent({ id: 'szb_close_fullscreen_modal' });
  };

  const handleResetRecommendation = () => {
    handleClose();
    postMessageToParent({ id: 'szb_reset_persona_hash' });
  };

  useLocalStorage(reset, setDetailsSaved);

  usePostMessage(
    {
      szb_close_form: handleClose,
    },
    [isOpen]
  );

  return (
    <Drawer open={isOpen} className="bg-white h-full">
      <DrawerContent
        position="bottom"
        size="auto"
        className="min-h-[440px] max-h-[440px] h-full md:max-h-[620px] md:min-h-[620px]"
        onClickOutside={handleClickOutside}
      >
        <SizeRecommendationForm resetRecommendation={handleResetRecommendation} onClose={handleClose} />
      </DrawerContent>
    </Drawer>
  );
}
