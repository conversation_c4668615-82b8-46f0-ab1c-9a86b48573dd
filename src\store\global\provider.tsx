import { FORM_INITIAL_VALUES, FORM_STORAGE_KEY, STEP_STORAGE_KEY } from '@/constants/pocket-virtual-fitting-room';
import { updateLocalStorage } from '@/hooks';
import { PocketVFRFormData, PocketVFRStep } from '@/types/pocket-virtual-fitting-room';
import React, { useEffect, useMemo, useReducer } from 'react';
import { GlobalContext } from './context';
import { GlobalActionTypes, initialState, sessionReducer } from './reducer';
import { GlobalContextData } from './types';

interface GlobalProviderProps {
  children: React.ReactNode;
}

export function GlobalProvider({ children }: GlobalProviderProps) {
  const [state, dispatch] = useReducer(sessionReducer, initialState);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data) {
        switch (event.data.id) {
          case 'szb_sync_recommendation': {
            const { sid, personaHash } = event.data;
            setSessionInfo(sid, personaHash);
            updateLocalStorage(FORM_STORAGE_KEY, JSON.stringify(FORM_INITIAL_VALUES));
            break;
          }
          case 'szb_cookies_update': {
            const { sid, personaHash } = event.data;
            setSessionInfo(sid, personaHash);
            break;
          }
          case 'szb_error_creating_profile': {
            dispatch({
              kind: GlobalActionTypes.SET_ERROR_PROFILE,
              payload: true,
            });
            break;
          }
        }
      }
    };

    window.parent.postMessage({ id: 'szb_iframe_context_ready' }, '*');
    window.addEventListener('message', handleMessage);

    return () => window.removeEventListener('message', handleMessage);
  }, []);

  useEffect(() => {
    const handler = (e: StorageEvent) => {
      if (e.key === STEP_STORAGE_KEY) {
        const newStep = Number(e.newValue);
        if (!isNaN(newStep)) {
          setCurrentStep(newStep);
        }
      }
    };
    window.addEventListener('storage', handler);
    return () => window.removeEventListener('storage', handler);
  }, []);

  const setErrorProfile = (value: boolean | null) => {
    dispatch({
      kind: GlobalActionTypes.SET_ERROR_PROFILE,
      payload: value,
    });
  };

  const setSessionInfo = (sessionId: string, personaHash: string) => {
    dispatch({
      kind: GlobalActionTypes.GET_SESSION_INFO,
      payload: {
        sessionId,
        personaHash,
      },
    });
  };

  const setPersonaHash = (value: string | null) => {
    dispatch({
      kind: GlobalActionTypes.SET_PERSONA_HASH,
      payload: value,
    });
  };

  const setPreviousSizeNotFound = (value: boolean) => {
    dispatch({
      kind: GlobalActionTypes.SET_PREVIOUS_SIZE_NOT_FOUND,
      payload: value,
    });
  };

  const setCurrentStep = (value: PocketVFRStep) => {
    dispatch({
      kind: GlobalActionTypes.SET_CURRENT_STEP,
      payload: value,
    });
  };

  const setLoadingRecommendation = (value: boolean) => {
    dispatch({
      kind: GlobalActionTypes.SET_LOADING_RECOMMENDATION,
      payload: value,
    });
  };

  const setDetailsSaved = (value: PocketVFRFormData) => {
    dispatch({
      kind: GlobalActionTypes.SET_DETAILS_SAVED,
      payload: value,
    });
  };

  const contextValue = useMemo(
    () =>
      ({
        state,
        actions: {
          setCurrentStep,
          setErrorProfile,
          setLoadingRecommendation,
          setPersonaHash,
          setPreviousSizeNotFound,
          setDetailsSaved,
        },
      }) as GlobalContextData,
    [state]
  );

  return <GlobalContext.Provider value={contextValue}>{children}</GlobalContext.Provider>;
}
