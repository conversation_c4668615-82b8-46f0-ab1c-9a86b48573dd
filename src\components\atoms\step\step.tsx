import React, { createContext } from 'react';
import { StepContent } from './step-content';
import { StepFooter } from './step-footer';
import { StepProps } from './types';
import { cn } from '@/utils/class-utils';
import { DeviceType } from '@/types/devices';
import { useDevice } from '@/hooks';

const StepContext = createContext<StepProps | undefined>(undefined);

export const Step = ({ children, className }: StepProps) => {
  const device = useDevice() as DeviceType;

  if (!children) {
    throw new Error('Step component requires children to render.');
  }

  const hasContent = React.Children.toArray(children).some(
    (child) => React.isValidElement(child) && child.type === StepContent
  );
  const hasFooter = React.Children.toArray(children).some(
    (child) => React.isValidElement(child) && child.type === StepFooter
  );

  if (!hasContent || !hasFooter) {
    throw new Error('Step component must contain Step<PERSON>ontent and StepFooter.');
  }

  return (
    <StepContext.Provider value={{ children, className }}>
      <div
        className={cn(
          'bg-white p-4 flex flex-col',
          {
            ['rounded-[8px] gap-2']: device !== DeviceType.Mobile && device !== DeviceType.SmallTablet,
          },
          {
            ['gap-5 h-full border-t rounded-t-lg min-w-full']:
              device === DeviceType.Mobile || device === DeviceType.SmallTablet,
          },
          className
        )}
      >
        {children}
      </div>
    </StepContext.Provider>
  );
};
