import { FORM_STORAGE_KEY, SIZE_GUIDE_KEYS } from '@/constants/pocket-virtual-fitting-room';
import { PocketVFRFormData, SizeGuideKeys } from '@/types/pocket-virtual-fitting-room';
import { useEffect } from 'react';
import { UseFormReset } from 'react-hook-form';
import { deleteCategoryData } from './use-pocket-vfr-form';

export function useLocalStorage(
  reset: UseFormReset<PocketVFRFormData>,
  setDetailsSaved: (value: PocketVFRFormData) => void
) {
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === FORM_STORAGE_KEY) {
        if (event.newValue) {
          const parsed = JSON.parse(event.newValue);
          reset(parsed);
        } else {
          deleteCategoryData(reset, SIZE_GUIDE_KEYS.clothingMeasures as SizeGuideKeys, setDetailsSaved);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);
}

export function updateLocalStorage(storageKey: string, storageValue: string) {
  localStorage.setItem(storageKey, storageValue);
}
