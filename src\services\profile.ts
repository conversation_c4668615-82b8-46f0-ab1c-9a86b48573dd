import { ClothingMeasures } from '@/types/pocket-virtual-fitting-room';

export interface Profile {
  sessionId: string;
  currentProfile: CurrentProfile;
}

export interface CurrentProfile {
  id: string;
  measures: Measures;
}

export interface Measures {
  gender: string;
  clothingMeasures: ClothingMeasures;
}

export async function getProfile(sessionId?: string): Promise<Profile> {
  const urlParams = new URLSearchParams(window.location.search);
  const sid = sessionId || urlParams.get('sessionId');
  const baseUrl = urlParams.get('baseUrl');

  if (!sid || !baseUrl) {
    throw new Error('Missing sessionId or baseUrl in query parameters');
  }

  const response = await fetch(`${baseUrl}api/me?sid=${sid}`);
  if (!response.ok) {
    throw new Error('Network response was not ok');
  }

  const data = await response.json();
  const measures = data?.currentProfile?.measures;

  return {
    sessionId: sid,
    currentProfile: {
      id: data?.currentProfile?.id,
      measures: {
        gender: measures?.gender,
        clothingMeasures: {
          age: measures?.age,
          height: measures?.height,
          weight: measures?.weight,
          composedMeasures: {
            chest: measures?.chest,
            waist: measures?.waist,
            hip: measures?.hip,
            bodyShapeChest: measures?.bodyShapeChest,
            bodyShapeWaist: measures?.bodyShapeWaist,
            bodyShapeHip: measures?.bodyShapeHip,
          },
        },
      },
    },
  };
}
