import { useEffect, useMemo, useState } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Button, Text, Tooltip } from '@/components/atoms';
import { GraduationCapIcon } from '@/components/icons';
import { Popover, PopoverContent, PopoverTrigger, TextFilter } from '@/components/molecules';
import { SizeRecommendationForm } from '@/components/organisms';
import { postMessageToParent, redirectOnClose, syncedStep, useDevice } from '@/hooks';
import { useGlobalContext } from '@/store/global';
import { DeviceType } from '@/types/devices';
import { GenericStep, PocketVFRFormData } from '@/types/pocket-virtual-fitting-room';
import { cn } from '@/utils/class-utils';

export const SizeGuideSection = () => {
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [hasProfileRecommendation, setHasProfileRecommendation] = useState(false);
  const { reset } = useFormContext<PocketVFRFormData>();
  const { t } = useTranslation();

  const {
    state: { sessionId, personaHash, errorCreatingProfile, previousSizeNotFound, currentStep, detailsSaved },
    actions: { setCurrentStep },
  } = useGlobalContext();

  const isSmallTablet = useDevice(DeviceType.SmallTablet) as boolean;
  const isMobile = useDevice(DeviceType.Mobile) as boolean;

  useEffect(() => {
    if (sessionId && personaHash) {
      setHasProfileRecommendation(true);
    }
  }, [sessionId, personaHash]);

  const stepToRedirect = useMemo(() => {
    const { clothingMeasures } = detailsSaved;
    const { age, height, weight } = clothingMeasures || {};
    const hasDetails = height && weight && age ? true : false;

    return redirectOnClose(currentStep, hasDetails);
  }, [currentStep, detailsSaved]);

  const handleOpenChange = (open: boolean) => {
    setPopoverOpen(open);
    if (open) {
      syncedStep(stepToRedirect, setCurrentStep);
    } else if (currentStep !== GenericStep.SELECT_CATEGORY) {
      reset(detailsSaved);
    }
  };

  const resetPersonaHash = () => {
    postMessageToParent({ id: 'szb_reset_persona_hash' });
  };

  const handleToggle = () => {
    if (isSmallTablet || isMobile) {
      postMessageToParent({
        id: 'szb_toggle_drawer',
      });
    }
    if (!isSmallTablet && !isMobile) {
      if (popoverOpen) {
        syncedStep(stepToRedirect, setCurrentStep);
        if (currentStep !== GenericStep.SELECT_CATEGORY) {
          reset(detailsSaved);
        }
      }
      setPopoverOpen(!popoverOpen);
    }
  };

  return (
    <div className={cn('flex justify-end items-center w-full gap-2', { 'justify-between': personaHash })}>
      {hasProfileRecommendation && (
        <TextFilter
          text={t('fashion_hint.products_with_your_size')}
          onClose={resetPersonaHash}
          className="px-2 py-1 whitespace-nowrap"
        />
      )}
      <div className={cn({ 'ml-auto': isMobile && personaHash, 'w-full': isMobile && !personaHash })}>
        <Popover className="relative flex justify-end w-full" open={popoverOpen} onOpenChange={handleOpenChange}>
          <PopoverTrigger asChild>
            <Tooltip
              content={t('pocket_vfr.error.size_not_found')}
              showAndFade
              delay={7000}
              placement="bottom"
              contentClassName="w-full"
              rootClassName={cn({
                'w-full': isMobile,
                'form-width': !personaHash,
              })}
              disabled={!(!errorCreatingProfile && !personaHash && previousSizeNotFound)}
            >
              <Button
                id="size-guide-trigger"
                variant="blank"
                fullWidth={!personaHash}
                onClick={handleToggle}
                className={cn('rounded-lg border border-[#EBEBEB] p-2 w-full fh-size__guide__button', {
                  'form-width': !personaHash && !isMobile,
                })}
              >
                <GraduationCapIcon className="fh-size__guide__button__icon" />
                {!personaHash && !hasProfileRecommendation && (
                  <Text as="span" size="sm" weight="medium" className="text-nowrap fh-size__guide__button__text">
                    {t('fashion_hint.virtual_fitting_room')}
                  </Text>
                )}
              </Button>
            </Tooltip>
          </PopoverTrigger>
          {!isSmallTablet && !isMobile && (
            <PopoverContent
              position="bottom"
              className={cn('rounded-[8px] form-width', {
                'w-[19rem] min-w-full': isMobile,
              })}
              align={'end'}
              onClickOutside={() => syncedStep(stepToRedirect, setCurrentStep)}
            >
              <SizeRecommendationForm resetRecommendation={resetPersonaHash} />
            </PopoverContent>
          )}
        </Popover>
      </div>
    </div>
  );
};
