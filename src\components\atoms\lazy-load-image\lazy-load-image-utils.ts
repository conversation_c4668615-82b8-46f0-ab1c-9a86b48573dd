export function autoCropImage(img: HTMLImageElement): Promise<string> {
  return new Promise((resolve) => {
    const w = img.naturalWidth;
    const h = img.naturalHeight;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    canvas.width = w;
    canvas.height = h;
    ctx.drawImage(img, 0, 0);

    const imageData = ctx.getImageData(0, 0, w, h);
    const pixels = imageData.data;

    let top = 0,
      bottom = h,
      left = 0,
      right = w;

    const isWhite = (i: number) => pixels[i] === 255 && pixels[i + 1] === 255 && pixels[i + 2] === 255;

    outerTop: for (let y = 0; y < h; y++) {
      for (let x = 0; x < w; x++) {
        const i = (y * w + x) * 4;
        if (!isWhite(i)) {
          top = y;
          break outerTop;
        }
      }
    }

    outerBottom: for (let y = h - 1; y >= 0; y--) {
      for (let x = 0; x < w; x++) {
        const i = (y * w + x) * 4;
        if (!isWhite(i)) {
          bottom = y;
          break outerBottom;
        }
      }
    }

    outerLeft: for (let x = 0; x < w; x++) {
      for (let y = top; y < bottom; y++) {
        const i = (y * w + x) * 4;
        if (!isWhite(i)) {
          left = x;
          break outerLeft;
        }
      }
    }

    outerRight: for (let x = w - 1; x >= 0; x--) {
      for (let y = top; y < bottom; y++) {
        const i = (y * w + x) * 4;
        if (!isWhite(i)) {
          right = x;
          break outerRight;
        }
      }
    }

    const cropWidth = right - left;
    const cropHeight = bottom - top;

    if (cropWidth === w && cropHeight === h) {
      resolve(img.src);
      return;
    }

    const croppedCanvas = document.createElement('canvas');
    const croppedCtx = croppedCanvas.getContext('2d')!;
    croppedCanvas.width = cropWidth;
    croppedCanvas.height = cropHeight;
    croppedCtx.drawImage(canvas, left, top, cropWidth, cropHeight, 0, 0, cropWidth, cropHeight);

    resolve(croppedCanvas.toDataURL());
  });
}
