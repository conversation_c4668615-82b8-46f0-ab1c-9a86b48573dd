import { useEffect, useRef, useState } from 'react';
import { TooltipProps } from './tooltip.types';
import { Slot } from '../slot';
import { cn } from '@/utils/class-utils';

const TooltipStyles = {
  base: 'absolute z-50 rounded-md bg-[#FFFFFF] text-[#272727] px-3 py-2 text-sm shadow-md break-words animate-in fade-in-50 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 pointer-events-none',
  placement: {
    top: 'bottom-[calc(100%+8px)] left-1/2 -translate-x-1/2 data-[state=closed]:slide-out-to-top-2 data-[state=open]:slide-in-from-bottom-2',
    bottom:
      'top-[calc(100%+8px)] left-1/2 -translate-x-1/2 data-[state=closed]:slide-out-to-bottom-2 data-[state=open]:slide-in-from-top-2',
    left: 'right-[calc(100%+8px)] top-1/2 -translate-y-1/2 data-[state=closed]:slide-out-to-left-2 data-[state=open]:slide-in-from-right-2',
    right:
      'left-[calc(100%+8px)] top-1/2 -translate-y-1/2 data-[state=closed]:slide-out-to-right-2 data-[state=open]:slide-in-from-left-2',
  },
  arrow: {
    top: 'bottom-[-4px] left-1/2 -translate-x-1/2 rotate-45 border-b border-r border-[#D9D9D9]',
    bottom: 'top-[-4px] left-1/2 -translate-x-1/2 rotate-45 border-t border-l border-[#D9D9D9]',
    left: 'right-[-4px] top-1/2 -translate-y-1/2 rotate-45 border-r border-t border-[#D9D9D9]',
    right: 'left-[-4px] top-1/2 -translate-y-1/2 rotate-45 border-l border-b border-[#D9D9D9]',
  },
};

export function Tooltip({
  content,
  children,
  placement = 'top',
  delay = 0,
  contentClassName: className,
  arrowClassName,
  disabled = false,
  showAndFade = false,
  rootClassName,
  ...props
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(showAndFade && !disabled);
  const [isAnimating, setIsAnimating] = useState(showAndFade && !disabled);

  const fadeOutTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const animationDelay = 10;
  const transitionDuration = 300;

  const startAnimation = () => {
    setIsVisible(true);
    setTimeout(() => setIsAnimating(true), animationDelay);
  };

  const endAnimation = () => {
    setIsAnimating(false);
    setTimeout(() => setIsVisible(false), transitionDuration);
  };

  useEffect(() => {
    const shouldShowAndFade = showAndFade && delay > 0 && !disabled;
    if (!shouldShowAndFade) {
      if (disabled) {
        setIsAnimating(false);
        setIsVisible(false);
      }
      return;
    }

    setIsAnimating(true);
    setIsVisible(true);

    fadeOutTimer.current = setTimeout(() => {
      endAnimation();
    }, delay);

    const cleanup = () => {
      if (fadeOutTimer.current) {
        clearTimeout(fadeOutTimer.current);
      }
    };

    return cleanup;
  }, [showAndFade, delay, disabled]);

  const showTooltip = () => {
    // Don't show tooltip if disabled is true, regardless of showAndFade status
    if (disabled) return;

    // Don't handle mouse events if showAndFade is true as it's controlled by the useEffect
    if (showAndFade) return;

    const shouldDelay = Boolean(delay);
    if (!shouldDelay) {
      startAnimation();
      return;
    }

    const timer = setTimeout(startAnimation, delay);
    return () => clearTimeout(timer);
  };

  const hideTooltip = () => {
    if (showAndFade) return;
    endAnimation();
  };

  return (
    <div className={cn('relative inline-block', rootClassName)}>
      <Slot onMouseEnter={showTooltip} onMouseLeave={hideTooltip} onFocus={showTooltip} onBlur={hideTooltip} {...props}>
        {children}
      </Slot>
      {isVisible && (
        <div
          data-state={isAnimating ? 'open' : 'closed'}
          className={cn(
            TooltipStyles.base,
            TooltipStyles.placement[placement],
            'border border-[#D9D9D9] transition-opacity duration-300 ease-in-out',
            isAnimating ? 'opacity-100' : 'opacity-0',
            className
          )}
          role="tooltip"
        >
          {content}
          <div
            className={cn(
              'absolute h-2 w-2 bg-[#FFFFFF] transition-opacity duration-300',
              arrowClassName,
              TooltipStyles.arrow[placement]
            )}
          />
        </div>
      )}
    </div>
  );
}
