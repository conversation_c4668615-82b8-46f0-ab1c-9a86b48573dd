import { useGlobalContext } from '@/store/global';
import { useForm, UseFormReset } from 'react-hook-form';
import {
  FORM_INITIAL_VALUES,
  FORM_STORAGE_KEY,
  SIZE_GUIDE_KEYS,
  STEP_STORAGE_KEY,
} from '../constants/pocket-virtual-fitting-room';
import {
  ClothingStep,
  GenericStep,
  PocketVFRFormData,
  PocketVFRStep,
  SizeGuideKeys,
} from '../types/pocket-virtual-fitting-room';
import { updateLocalStorage } from './use-local-storage';

export function usePocketVFRForm() {
  const {
    state: { detailsSaved },
  } = useGlobalContext();
  const methods = useForm<PocketVFRFormData>({
    mode: 'onSubmit',
    defaultValues: detailsSaved,
  });
  const { register, getValues, setValue, watch, handleSubmit, reset } = methods;

  return {
    methods,
    handleSubmit,
    getValues,
    watch,
    register,
    reset,
    setValue,
  };
}

export function deleteCategoryData(
  reset: UseFormReset<PocketVFRFormData>,
  categoryToDelete: SizeGuideKeys,
  setDetailsSaved: (value: PocketVFRFormData) => void
): void {
  if (categoryToDelete === SIZE_GUIDE_KEYS.clothingMeasures) {
    reset(FORM_INITIAL_VALUES);
    syncedState(FORM_INITIAL_VALUES, setDetailsSaved, FORM_STORAGE_KEY);
    return;
  }

  reset({ [SIZE_GUIDE_KEYS[categoryToDelete]]: undefined });
}

export function syncedState(stateToSync: PocketVFRFormData | PocketVFRStep, setStateToSync: (value: PocketVFRFormData | PocketVFRStep) => void, storageKey: string) {
  updateLocalStorage(storageKey, storageKey === STEP_STORAGE_KEY ? String(stateToSync) : JSON.stringify(stateToSync));
  setStateToSync(stateToSync);
}

export function syncedDetails(details: PocketVFRFormData, setDetailsSaved: (value: PocketVFRFormData) => void) {
  localStorage.setItem(FORM_STORAGE_KEY, JSON.stringify(details));
  setDetailsSaved(details);
}

export function syncedStep(currentStep: PocketVFRStep, setCurrentStep: (value: PocketVFRStep) => void) {
  localStorage.setItem(STEP_STORAGE_KEY, String(currentStep));
  setCurrentStep(currentStep);
}

export function redirectOnClose(currentStep: PocketVFRStep, hasDetails: boolean) {
  return !hasDetails && currentStep !== GenericStep.SELECT_CATEGORY
    ? ClothingStep.BODY_MEASURES
    : GenericStep.SELECT_CATEGORY;
}
