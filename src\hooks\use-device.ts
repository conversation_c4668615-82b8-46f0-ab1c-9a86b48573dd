import { useEffect, useState } from 'react';
import { debounce } from '../utils/debounce-utils';
import { DeviceType } from '@/types/devices';

const DeviceBreakpoints = {
  [DeviceType.Desktop]: 1024,
  [DeviceType.Laptop]: 768,
  [DeviceType.Tablet]: 639,
  [DeviceType.SmallTablet]: 441,
  [DeviceType.Mobile]: 0,
};

/**
 * A custom hook to determine the current device type based on the window width.
 * Or to validate if the current device matches a specified type.
 *
 * @param validateType - The device type to validate against. If not provided, it will return the current device type.
 * @example
 * ```tsx
 * const isMobile = useDevice(DeviceType.Mobile);
 * const isTablet = useDevice(DeviceType.Tablet);
 * const isDesktop = useDevice(DeviceType.Desktop);
 * const isSmallMobile = useDevice(DeviceType.SmallMobile);
 * const currentDevice = useDevice(); // Returns the current device type
 * ```
 * @returns The current device type or a boolean indicating if the current device matches the specified type.
 */
export function useDevice(validateType?: DeviceType) {
  const [currentDevice, setCurrentDevice] = useState(getDeviceType());

  useEffect(() => {
    const handleResize = () => {
      const newDeviceType = getDeviceType();
      setCurrentDevice(newDeviceType);
    };

    const dbHandleResize = debounce(handleResize, 150);

    window.addEventListener('resize', dbHandleResize);
    return () => window.removeEventListener('resize', dbHandleResize);
  }, []);

  return !validateType ? currentDevice : currentDevice === validateType;
}

const getDeviceType = () => {
  const width = window.innerWidth;

  if (width >= DeviceBreakpoints[DeviceType.Desktop]) {
    return DeviceType.Desktop;
  }
  if (width >= DeviceBreakpoints[DeviceType.Laptop]) {
    return DeviceType.Laptop;
  }
  if (width >= DeviceBreakpoints[DeviceType.Tablet]) {
    return DeviceType.Tablet;
  }
  if (width >= DeviceBreakpoints[DeviceType.SmallTablet]) {
    return DeviceType.SmallTablet;
  }

  return DeviceType.Mobile;
};
