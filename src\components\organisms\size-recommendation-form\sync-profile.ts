import { FORM_STORAGE_KEY } from '@/constants/pocket-virtual-fitting-room';
import { syncedState } from '@/hooks/use-pocket-vfr-form';
import { getProfile } from '@/services/profile';
import { Gender, PocketVFRFormData } from '@/types/pocket-virtual-fitting-room';
import { normalizeClothingMeasures } from '@/utils/measurement-conversions';

const INVALID_PROFILE_ID = 0;

export async function syncProfile(sessionId: string, setDetailsSaved: (value: PocketVFRFormData) => void) {
  try {
    const profile = await getProfile(sessionId);

    if (!profile?.currentProfile) return;

    const { measures, id } = profile.currentProfile;

    if (Number(id) === INVALID_PROFILE_ID) return;

    if (!measures?.clothingMeasures) return;

    const { gender, clothingMeasures } = measures;
    const { composedMeasures } = clothingMeasures;

    const { bodyShapeChest, bodyShapeWaist, bodyShapeHip, height, weight, age } = normalizeClothingMeasures(
      clothingMeasures,
      true
    );

    syncedState(
      {
        isMetric: true,
        gender: gender as Gender,
        clothingMeasures: {
          height,
          weight,
          age,
          composedMeasures: {
            bodyShapeChest,
            bodyShapeWaist,
            bodyShapeHip,
            chest: composedMeasures.chest,
            waist: composedMeasures.waist,
            hip: composedMeasures.hip,
          },
        },
      },
      setDetailsSaved,
      FORM_STORAGE_KEY
    );
  } catch (error) {
    console.error('Error fetching user profile:', error);
  }
}
