@tailwind base;
@tailwind components;
@tailwind utilities;

#root {
  margin: 0;
  padding: 0;
}

@layer components {
  .carousel-container {
    /* fallback total */
    @apply relative w-full max-w-full;

    /* === Mobile (até 639px): 2 cards originais === */
    /* seu cálculo original pra 2 cards: */
    @apply w-[calc(11.25rem*2+1rem+3rem*2)];

    /* === Small >=640px e <650px: idem (a maioria dos phones) */
    @apply sm:w-[calc(11.25rem*2+1rem+3rem*2)];

    /* === A partir de 650px (tablet “sm+”): 3 cards “esticados” === */
    @apply sm650:w-[calc(13.125rem*3+1.25rem*2)];
    
    /* card “md” = 13.125rem, gap-5 = 1.25rem */
    @apply md:w-[calc(13.125rem*3.5+1.25rem*2)];

    /* === A partir de 950px (tablet “md+”): 4 cards “esticados” === */
    @apply md900:w-[calc(13.125rem*4+2rem*2)];

    /* === Desktop (lg ≥1024px): volta aos 4 cards originais === */
    @apply lg:w-[calc(15rem*4+3rem+4rem*2)];
    @apply xl:w-[calc(16.25rem*4+3rem+4rem*2)];
    @apply 2xl:w-[calc(18.125rem*4+3rem+4rem*2)];
  }

  /* --------------- Complementary: sempre 3 cards --------------- */
  /* Wrapper complementary (sem mudanças) */
  .carousel-container-complementary {
    @apply w-full overflow-x-auto flex gap-4 sm:gap-4 md:gap-4 lg:gap-2 xl:gap-4 snap-x snap-mandatory scrollbar-none;
  }

  /* Cada slide do complementary, sem altura fixa! */
  .carousel-item-complementary {
    /* 1 card no mobile (<640px) */
    @apply flex-none w-full;
        @apply sm:w-[calc((100%-1rem)/2)]; 
    @apply md:w-[calc((100%-1rem)/2)];
    @apply md900:w-[calc((100%-2.1rem)/3)];
    @apply lg:w-[calc((100%-1rem)/3)] xl:w-[calc((100%-1rem*2)/3)];
    /* estéticas */
    @apply min-h-[15rem] lg:min-h-[18rem];
  }

  /* Imagem: cresce um pouco sem vazar */
  .image-card-variant,
  .image-card-variant-height {
    @apply w-full object-cover h-[6rem];
  }

  .similar-carousel-card {
    /* === Mobile: 2 colunas esticadas === */
    /* gap-5 (1.25rem) está no <CarouselContent />, então descontamos 1.25rem */
    @apply w-[calc((100%-1.25rem)/2)] max-w-none;

    /* === Entre 650px e 1023px: 3 colunas esticadas === */
    @apply sm650:w-[calc((100%-2.5rem)/3)] sm650:max-w-none;
    @apply md:w-[calc((100%-2.5rem)/3)] md:max-w-none;
    @apply md900:w-[calc((100%-3.75rem)/4)];

    @apply lg:w-[calc((100%-3.75rem)/4)] lg:max-w-none;

    /* === A partir de lg: volta aos max-widths fixos === */
    @apply xl:w-[calc((100%-3.75rem)/4)] xl:max-w-[16.25rem];
    @apply 2xl:w-[calc((100%-3.75rem)/4)] 2xl:max-w-[18.125rem];
  }

  .form-width {    
    @apply sm:w-[calc((35.50rem-1rem)/2)];
    @apply md:w-[calc((42rem-1rem)/2)];
    @apply md900:w-[calc((50rem-2.1rem)/3)];
    @apply lg:w-[calc((58rem-1rem)/3)];
    @apply xl:w-[calc((68rem-1rem)/3)];
    @apply 2xl:w-[calc((75rem-1rem)/3)];
  }

  .size-guide-trigger-width {
    @apply sm:w-[calc((100%-1rem)/2)]; 
    @apply md:w-[calc((100%-1rem)/2)];
    @apply md900:w-[calc((100%-2.1rem)/3)];
    @apply lg:w-[calc((100%-1rem)/3)] xl:w-[calc((100%-1rem*2)/3)];
  }

  .carousel-card-height {
    @apply h-[13.125rem];
    @apply sm:h-[13.125rem];
    @apply md:h-[15rem];
    @apply lg:h-[16.875rem];
    @apply xl:h-[18.125rem];
    @apply 2xl:h-[20rem];
  }

  .similar-carousel-card-width {
    @apply max-w-[9.375rem];
    @apply sm:max-w-[11.25rem] w-full;
    @apply md:max-w-[13.125rem] w-full;
    @apply lg:max-w-[15rem] w-full;
    @apply xl:max-w-[16.25rem] w-full;
    @apply 2xl:max-w-[18.125rem] w-full;
  }

  .body-shape-height {
    @apply h-[18.75rem];
    @apply sm:h-[12.5rem];
    @apply lg:h-[15.625rem];
    @apply xl:h-[18.75rem];
  }

  .body-shape-image-crop {
    @apply scale-100 xl:scale-[1] lg:scale-[1.11] md900:scale-[1.2] md:scale-[0.8];
    @apply translate-y-[3rem] 2xl:translate-y-[6rem] xl:translate-y-[4.9rem] lg:translate-y-[4.4rem] md900:translate-y-[3.4rem] md:translate-y-[3.9rem];
    @apply translate-x-1;
  }

}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;

    --radius: 0.5rem;

    /* Sidebar variables */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 240 10% 3.9%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 240 5.9% 90%;
    --sidebar-ring: 240 10% 3.9%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;

    /* Sidebar variables */
    --sidebar-background: 240 10% 3.9%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 240 4.9% 83.9%;
  }
}

@layer utilities {
  .scrollbar-none {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-none::-webkit-scrollbar {
    display: none;
  }

  .scroll-behavior-smooth {
    scroll-behavior: smooth;
  }
}

/* Animações do carrossel */
@keyframes slide-group {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-group-reverse {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@layer components {
  .animate-slide-group {
    animation: slide-group 0.5s ease-out;
  }

  .animate-slide-group-reverse {
    animation: slide-group-reverse 0.5s ease-out;
  }
}
