import { useFormContext } from 'react-hook-form';
import { Carousel } from '@/components/organisms';
import { useComplementaryProducts, useLocalStorage } from '@/hooks';
import { useGlobalContext } from '@/store/global';
import { CarouselVariant } from '@/types/carousel';
import { PocketVFRFormData } from '@/types/pocket-virtual-fitting-room';
import { useItemsPerPage } from '@/utils/items-per-page-utils';

export function ComplementarProductsPage() {
  const { products, isLoading } = useComplementaryProducts();
  const { reset } = useFormContext<PocketVFRFormData>();
  const itemsPerPage = useItemsPerPage({ itemsType: CarouselVariant.Complementary });
  const {
    state: { personaHash },
    actions: { setDetailsSaved },
  } = useGlobalContext();

  useLocalStorage(reset, setDetailsSaved);

  return (
    <Carousel
      isLoading={isLoading}
      products={products}
      isFetchingMore={false}
      itemsPerPage={itemsPerPage}
      type={CarouselVariant.Complementary}
      className={'fh-complementary'}
      key={personaHash}
    />
  );
}
