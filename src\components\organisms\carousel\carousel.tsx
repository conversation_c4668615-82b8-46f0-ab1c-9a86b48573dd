import { CarouselContent, CarouselNavigationButton } from '../../molecules';
import { SizeGuideSection } from '../size-guide-section';
import { PaginationDots } from '@/components/atoms';
import { useCarouselController } from '@/hooks/use-carousel-controller';
import { CarouselVariant } from '@/types/carousel';
import { ComplementaryProductsBundle, SimilarProduct } from '@/types/products';
import { cn } from '@/utils/class-utils';

interface CarouselProps {
  itemsPerPage: number;
  products: SimilarProduct[] | ComplementaryProductsBundle;
  isLoading: boolean;
  onLoadMore?: () => void;
  isFetchingMore?: boolean;
  className?: string;
  type: CarouselVariant;
  hasNextPage?: boolean;
}

const CarouselStyles = {
  wrapper: 'w-full flex justify-center overflow-hidden',
  container: {
    base: 'carousel-container',
    content: 'flex items-center justify-center gap-2',
    pagination: 'mt-4',
  },
};

export function Carousel({
  itemsPerPage,
  products,
  isLoading,
  onLoadMore,
  isFetchingMore,
  type,
  className,
  hasNextPage,
}: CarouselProps) {
  const { canScrollLeft, canScrollRight, contentRef, handlers, totalPages, currentPage } = useCarouselController({
    itemsPerPage,
    totalItems: products.length,
    onLoadMore,
    isFetchingMore: isFetchingMore ?? false,
    hasNextPage: hasNextPage ?? false,
  });

  const shouldShowNavigation = !isLoading && totalPages > 1;

  if (!isLoading && products.length === 0) return null;

  return (
    <div className={cn(CarouselStyles.wrapper, className)}>
      <div
        className={CarouselStyles.container.base}
        onKeyDown={(e) => handlers.keyboard.onKeyDown(e.nativeEvent)}
        role="region"
        aria-label="Product carousel"
        tabIndex={0}
      >
        <div className={CarouselStyles.container.content}>
          {shouldShowNavigation && (
            <CarouselNavigationButton
              direction="left"
              onClick={handlers.navigation.onPrevious}
              disabled={!canScrollLeft}
              aria-label="Previous page"
            />
          )}
          <div
            className={cn(
              'flex flex-col overflow-auto scrollbar-none gap-5 w-full',
              { ['px-12']: !shouldShowNavigation },
              { 'p-3': isLoading }
            )}
            ref={contentRef}
          >
            <SizeGuideSection />
            <CarouselContent products={products} isLoading={isLoading} contentRef={contentRef} type={type} />
          </div>
          {shouldShowNavigation && (
            <CarouselNavigationButton
              direction="right"
              onClick={handlers.navigation.onNext}
              disabled={!canScrollRight}
              aria-label="Next page"
            />
          )}
        </div>
        {shouldShowNavigation && (
          <PaginationDots
            totalPages={totalPages}
            currentPage={currentPage}
            onPageChange={handlers.navigation.onPageChange}
            className={CarouselStyles.container.pagination}
          />
        )}
      </div>
    </div>
  );
}
