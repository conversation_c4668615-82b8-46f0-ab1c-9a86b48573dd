/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect } from 'react';
import { FormProvider } from 'react-hook-form';
import { usePocketVFRForm, useQueryParams } from './hooks';
import { Router } from './router';
import { useGlobalContext } from './store/global';
import './styles/App.css';
import { injectTenantStyles } from './utils/injectTenantStyles';

function App() {
  const { methods, reset } = usePocketVFRForm();
  const { raw: params } = useQueryParams();
  const { tenantId } = params;

  const {
    state: { detailsSaved, personaHash },
    actions: { setDetailsSaved },
  } = useGlobalContext();

  useEffect(() => {
    if (detailsSaved) {
      reset(detailsSaved);
    }
    // else if (personaHash) {
    //   syncedDetails(FORM_INITIAL_VALUES, setDetailsSaved);
    // }
  }, [detailsSaved]);

  useEffect(() => {
    if (tenantId) {
      injectTenantStyles(tenantId);
    }
  }, [tenantId]);

  useEffect(() => {
    const el = document.querySelector<HTMLElement>('.fashion-hint-content');
    if (!el) return;

    const sendHeight = () => {
      const height = Math.max(el.scrollHeight, el.getBoundingClientRect().height);
      if (height === 0) return;
      window.parent.postMessage(
        {
          id: 'szb_content_height',
          height,
          context: window.location.pathname.includes('similar') ? 'similar' : 'complementary',
        },
        '*'
      );
    };

    sendHeight();

    const observer = new ResizeObserver(() => {
      sendHeight();
    });
    observer.observe(el);

    return () => observer.disconnect();
  }, []);

  return (
    <FormProvider {...methods}>
      <Router />
    </FormProvider>
  );
}

export default App;
