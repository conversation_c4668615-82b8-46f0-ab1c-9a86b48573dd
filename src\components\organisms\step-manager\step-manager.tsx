import { BodyMeasuresStep, BodyShapeStep, CategoryStep } from '@/components/organisms/step-manager';
import { useDevice } from '@/hooks';
import { useGlobalContext } from '@/store/global';
import { DeviceType } from '@/types/devices';
import { ClothingStep, GenericStep } from '@/types/pocket-virtual-fitting-room';

export const StepManager = () => {
  const {
    state: { currentStep },
  } = useGlobalContext();
  const device = useDevice() as DeviceType;
  const isSmallerDevices = [DeviceType.SmallTablet, DeviceType.Mobile].includes(device);

  return (
    <>
      {currentStep === GenericStep.SELECT_CATEGORY && <CategoryStep isSmallerDevices={isSmallerDevices} />}
      {currentStep === ClothingStep.BODY_MEASURES && <BodyMeasuresStep isSmallerDevices={isSmallerDevices} />}
      {currentStep === ClothingStep.BODY_SHAPE && <BodyShapeStep isSmallerDevices={isSmallerDevices} />}
    </>
  );
};
