const baseUrl = window.SizebayFashionHint.getBaseVrfUrl();
const tenantId = window.SizebayFashionHint.getTenantId();
const collectionName = window.SizebayFashionHint.getCollectionName();
const permalink = window.SizebayFashionHint.getPermalink();
const perPage = window.SizebayFashionHint.getPerPage();
const sizeSystem = window.SizebayFashionHint.getSizeSystem();
const lang = window.SizebayFashionHint.getLang();
const currency = window.SizebayFashionHint.getCurrency();
const defaultUnit = window.SizebayFashionHint.getDefaultUnit();
const similarContainerId = window.SizebayFashionHint.getSimilarContainerId();
const baseFashionHintUrl = window.SizebayFashionHint.getBaseFashionHintUrl();
const complementaryContainerId = window.SizebayFashionHint.getComplementaryContainerId();
const similarityThreshold = window.SizebayFashionHint.getSimilarityThreshold();

const page = 1;

(() => {
  const getCookie = (name) => {
    const cookiePattern = new RegExp('(^|;)\\s*' + name + '\\s*=\\s*' + '([^;]+)');
    const match = document.cookie.match(cookiePattern);
    return match ? match[2] : null;
  };

  const setCookie = (name, value) => {
    const domain = '; domain=' + window.location.hostname.replace('www.', '.');
    const expires = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toUTCString();
    document.cookie = `${encodeURIComponent(name)}=${encodeURIComponent(value)}; expires=${expires}; path=/${domain};`;
  };

  function deleteCookie(name) {
    const cookieName = encodeURIComponent(name);
    const domain = '; domain=' + window.location.hostname.replace('www.', '.');

    document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; ` + `path=/; domain=${domain};`;
  }

  function adjustIframeHeights() {
    const iframe = document.getElementById('fashion-hint-iframe-similar');
    if (!iframe) return;

    const defaultHeight = '60vh';
    iframe.style.height = defaultHeight;
  }

  const syncRecommendationEvent = new CustomEvent('fh:sync-recommendation', {
    detail: {
      permalink,
    },
  });

  window.addEventListener('message', (event) => {
    const data = event.data;

    if (
      (data?.id === 'szb_content_height' && data.context === 'similar') ||
      (data?.id === 'szb_content_height' && data.context === 'complementary')
    ) {
      if (!data.height) return;

      lastComplementaryHeight = data.height;
      const dynamicMargin = data.context === 'similar' ? 50 : 80;

      const iframe = document.getElementById(`fashion-hint-iframe-${data.context}`);
      if (iframe) {
        iframe.style.height = `${data.height + dynamicMargin}px`;
      }
    }
  });

  function adjustIframeComplementaryHeights() {
    const iframe = document.getElementById('fashion-hint-iframe-complementary');
    if (!iframe) return;

    const defaultHeight = '70vh';
    iframe.style.height = defaultHeight;
  }

  function getSafeViewportHeight() {
    if (window.visualViewport) {
      return window.visualViewport.height;
    }
    return window.innerHeight;
  }

  function adjustDrawerHeight() {
    const drawer = document.getElementById('fashion-hint-drawer');
    if (!drawer) return;

    const safeHeight = getSafeViewportHeight();
    drawer.style.height = `${safeHeight}px`;
  }

  function broadcastCookieUpdateToIframes(closeModal = false, syncRecommendationEvent = false) {
    const sid = getCookie('SIZEBAY_SESSION_ID_V4');
    const personaHash = getCookie('SIZEBAY_PERSONA_HASH');

    const message = {
      id: 'szb_cookies_update',
      sid,
      personaHash,
    };

    const similarIframe = document.getElementById('fashion-hint-iframe-similar');
    const complementaryIframe = document.getElementById('fashion-hint-iframe-complementary');
    const drawerIframe = document.getElementById('fashion-hint-iframe-drawer');

    if (syncRecommendationEvent) {
      Object.assign(message, { id: 'szb_sync_recommendation' });
    }

    if (similarIframe?.contentWindow) {
      similarIframe.contentWindow.postMessage(message, '*');
      similarIframe.contentWindow?.postMessage({ id: 'szb_recommendation_ready' }, '*');
    } else {
      console.warn('similarIframe não está pronto para receber mensagens.');
    }

    if (complementaryIframe?.contentWindow) {
      complementaryIframe.contentWindow.postMessage(message, '*');
      complementaryIframe.contentWindow?.postMessage({ id: 'szb_recommendation_ready' }, '*');
    }

    if (closeModal && drawerIframe?.contentWindow) {
      drawerIframe.contentWindow.postMessage({ id: 'szb_close_form' }, '*');
    }
  }

  const fetchSid = async () => {
    try {
      const baseUrl = window.SizebayFashionHint.getBaseVrfUrl();
      const response = await fetch(`${baseUrl}api/me/session-id`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch /api/me: ${response.statusText}`);
      }

      const sessionId = await response.json();

      if (sessionId) {
        setCookie('SIZEBAY_SESSION_ID_V4', sessionId);
        broadcastCookieUpdateToIframes();
      }

      return sessionId;
    } catch (error) {
      console.error('Error fetching user data from /api/me:', error);
      return null;
    }
  };

  const fetchUserProfile = async (profileData) => {
    document.cookie = 'SIZEBAY_PERSONA_HASH=;path=/;expires=Thu, 01 Jan 1970 00:00:00 UTC;Secure';
    const sid = getCookie('SIZEBAY_SESSION_ID_V4');

    if (!sid) throw new Error('SID não encontrado nos cookies');

    const response = await fetch(
      `${baseUrl}api/me/user/profile?sid=${sid}&pre_predicted=true&sizeSystem=${sizeSystem}`,
      {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          Accept: 'application/json',
          tenant_id: tenantId.toString(),
          device: 'desktop',
        },
        body: JSON.stringify({
          id: null,
          userId: sid,
          name: 'szb-profile-no-name',
          skinType: 0,
          footShape: null,
          gender: profileData?.gender,
          age: profileData?.age,
          is3dFeel: true,
          weight: profileData?.weight,
          height: profileData?.height,
          measures: {
            insoleLength: 0,
            poundWeight: null,
            hip: profileData?.hip,
            waist: profileData?.waist,
            chest: profileData?.chest,
          },
          product: null,
          isMetric: profileData.isMetric,
          bodyShapeChest: profileData?.bodyShapeChest,
          bodyShapeHip: profileData?.bodyShapeHip,
          bodyShapeWaist: profileData?.bodyShapeWaist,
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Erro ao criar perfil: ${response.statusText}`);
    }

    const responseData = await response.json();

    if (responseData?.persona_hash) {
      window.dispatchEvent(syncRecommendationEvent);

      setCookie('SIZEBAY_PERSONA_HASH', responseData?.persona_hash);
      broadcastCookieUpdateToIframes((closeModal = true));
    } else {
      const IframeMap = {
        similar: document.getElementById('fashion-hint-iframe-similar'),
        complementary: document.getElementById('fashion-hint-iframe-complementary'),
        drawer: document.getElementById('fashion-hint-iframe-drawer'),
      };

      for (const key in IframeMap) {
        const iframe = IframeMap[key];
        if (!iframe) continue;
        iframe.contentWindow?.postMessage({ id: 'szb_error_creating_profile' }, '*');
      }
    }
    return responseData;
  };

  const generateIframe = () => {
    let sessionId = getCookie('SIZEBAY_SESSION_ID_V4');

    const containerSimilar = document.getElementById(similarContainerId);
    containerSimilar.innerHTML = '';
    const iframeSimilar = document.createElement('iframe');

    const containerComplementary = document.getElementById(complementaryContainerId);
    containerComplementary.innerHTML = '';
    const iframeComplementary = document.createElement('iframe');

    iframeSimilar.style.width = '100%';
    iframeSimilar.style.border = 'none';

    iframeComplementary.style.width = '100%';
    iframeComplementary.style.border = 'none';

    iframeSimilar.src = `${baseFashionHintUrl}similar?baseUrl=${baseUrl}&defaultUnit=${defaultUnit}&sessionId=${sessionId}&tenantId=${tenantId}&collectionName=${collectionName}&permalink=${permalink}&sizeSystem=${sizeSystem}&lang=${lang}&currency=${currency}&page=${page}&perPage=${perPage}&similarityThreshold=${similarityThreshold}`;
    iframeSimilar.id = 'fashion-hint-iframe-similar';

    iframeComplementary.src = `${baseFashionHintUrl}complementary?baseUrl=${baseUrl}&defaultUnit=${defaultUnit}&sessionId=${sessionId}&tenantId=${tenantId}&collectionName=${collectionName}&permalink=${permalink}&sizeSystem=${sizeSystem}&lang=${lang}&currency=${currency}&page=${page}&perPage=${perPage}&similarityThreshold=${similarityThreshold}`;

    iframeComplementary.id = 'fashion-hint-iframe-complementary';

    containerSimilar.appendChild(iframeSimilar);
    adjustIframeHeights();

    containerComplementary.appendChild(iframeComplementary);
    adjustIframeComplementaryHeights();

    iframeSimilar.onload = () => {
      window.addEventListener('message', async (event) => {
        if (event.data && event.data.id === 'szb_iframe_context_ready') {
          broadcastCookieUpdateToIframes();
        }
      });
    };
    iframeComplementary.onload = () => {
      window.addEventListener('message', async (event) => {
        if (event.data && event.data.id === 'szb_iframe_context_ready') {
          broadcastCookieUpdateToIframes();
        }
      });
    };
  };

  const initialize = async () => {
    let sessionId = getCookie('SIZEBAY_SESSION_ID_V4');

    if (!sessionId) {
      sessionId = await fetchSid();
    }

    if (sessionId) {
      await generateIframe();
    } else {
      console.error('Failed to retrieve sessionId. Cannot generate iframe.');
    }
  };

  initialize();

  document.addEventListener('mousedown', (event) => {
    const iframe = document.getElementById('fashion-hint-iframe-similar');
    if (!iframe) return;

    const clickedInsideIframe = iframe.contains(event.target);
    if (!clickedInsideIframe) {
      iframe.contentWindow?.postMessage({ id: 'szb_close_size_guide' }, '*');
    }
  });

  window.addEventListener('message', async (event) => {
    if (event.data && typeof event.data.id === 'string' && event.data.id.includes('szb_')) {
      switch (event.data.id) {
        case 'szb_toggle_drawer': {
          const drawer = document.getElementById('fashion-hint-drawer');

          if (!drawer) {
            const newDrawer = document.createElement('div');
            newDrawer.id = 'fashion-hint-drawer';
            newDrawer.style.position = 'fixed';
            newDrawer.style.top = '0';
            newDrawer.style.right = '0';
            newDrawer.style.left = '0';
            newDrawer.style.width = '100%';
            newDrawer.style.zIndex = '9999';
            document.body.appendChild(newDrawer);

            const iframeDrawer = document.createElement('iframe');
            let sessionId = getCookie('SIZEBAY_SESSION_ID_V4');

            iframeDrawer.src = `${baseFashionHintUrl}szb-drawer?baseUrl=${baseUrl}&defaultUnit=${defaultUnit}&sessionId=${sessionId}&tenantId=${tenantId}&collectionName=${collectionName}&permalink=${permalink}&sizeSystem=${sizeSystem}&lang=${lang}&currency=${currency}&page=${page}&perPage=${perPage}`;

            iframeDrawer.id = 'fashion-hint-iframe-drawer';
            iframeDrawer.style.width = '100%';
            iframeDrawer.style.height = '100%';
            iframeDrawer.style.border = 'none';

            newDrawer.appendChild(iframeDrawer);
            adjustDrawerHeight();
            return;
          }

          if (drawer) {
            drawer.remove();
          }

          break;
        }
        case 'szb_close_fullscreen_modal': {
          const drawer = document.getElementById('fashion-hint-drawer');
          if (drawer) drawer.remove();
          break;
        }
        case 'szb_recommendation_not_found': {
          const TypeMap = {
            similar: document.getElementById('fashion-hint-iframe-similar'),
            complementary: document.getElementById('fashion-hint-iframe-complementary'),
          };
          const iframeId = TypeMap[event.data.type];
          if (iframeId) {
            const iframe = document.getElementById(iframeId);
            iframe.remove();
          }
          break;
        }
        case 'szb_reset_persona_hash': {
          deleteCookie('SIZEBAY_PERSONA_HASH');
          deleteCookie('SIZEBAY_SESSION_ID_V4');
          broadcastCookieUpdateToIframes();
          generateIframe();
          await fetchSid();
          window.dispatchEvent(syncRecommendationEvent);
          break;
        }
        case 'szb_get_recommended_size': {
          try {
            const userMeasurements = event?.data?.data;
            await fetchUserProfile(userMeasurements);
          } catch (error) {
            console.error('Error fetching user profile:', error);
          }
          break;
        }
        case 'szb_click_product': {
          const productLink = event.data.productLink;
          const type = event.data.type;
          if (productLink) {
            if (type === 'complementary') {
              window.open(productLink, '_blank');
            }
            if (type === 'similar') {
              window.open(productLink, '_self');
            }
          } else {
            console.warn('Product link is not available.');
          }
          break;
        }
      }
    }
  });

  window.addEventListener('vfr:sync-recommendation', async () => {
    await broadcastCookieUpdateToIframes(syncRecommendationEvent = true);
  });
})();
