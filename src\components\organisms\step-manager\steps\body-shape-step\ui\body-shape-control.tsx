import { Button } from '@/components/atoms';
import { MinusIcon, PlusIcon } from '@/components/icons';
import { SizeGuideKeys } from '@/types/pocket-virtual-fitting-room';

interface Props {
  shape: SizeGuideKeys;
  operationType: number;
  isDisabled: boolean;
  onClick: (shape: SizeGuideKeys, operationType: number) => void;
}

export const BodyShapeControl = ({ shape, operationType, isDisabled, onClick }: Props) => {
  const isIncrement = operationType > 0;
  return (
    <Button
      disabled={isDisabled}
      variant="blank"
      size="sm"
      className="px-0"
      aria-label={`${isIncrement ? 'Increase' : 'Decrease'} ${shape}`}
      onClick={() => onClick(shape, operationType)}
    >
      {isIncrement ? <PlusIcon /> : <MinusIcon />}
    </Button>
  );
};
