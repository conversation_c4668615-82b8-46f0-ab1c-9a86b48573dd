import { Gender } from './pocket-virtual-fitting-room';

export interface GetRecommendedSizeData {
  gender: Gender;
  height: string;
  isMetric: boolean;
  weight: string;
  age: string;
  bodyShapeChest: number;
  bodyShapeWaist: number;
  bodyShapeHip: number;
  chest: number;
  waist: number;
  hip: number;
}

export type PostMessageToParent =
  | { id: 'szb_get_recommended_size'; data: GetRecommendedSizeData }
  | { id: 'szb_close_size_guide' }
  | { id: 'szb_recommendation_ready' }
  | { id: 'szb_cookies_update'; sid?: string | null; personaHash?: string | null }
  | { id: 'szb_close_form' }
  | { id: 'szb_error_creating_profile' }
  | { id: 'szb_content_height'; context: 'similar' | 'complementary'; height: number }
  | { id: 'szb_iframe_context_ready' }
  | { id: 'szb_toggle_drawer' }
  | { id: 'szb_close_fullscreen_modal' }
  | { id: 'szb_recommendation_not_found'; type: 'similar' | 'complementary' }
  | { id: 'szb_reset_persona_hash' }
  | { id: 'szb_click_product'; productLink: string; type: 'similar' | 'complementary' };
