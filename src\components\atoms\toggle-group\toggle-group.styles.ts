import { ToggleSize } from './toggle-group.types';

export const toggleGroupBaseStyles = 'flex items-center gap-1 rounded-lg';

export const toggleGroupFullWidthStyles = 'w-full';

export const toggleGroupSizeStyles: Record<ToggleSize, string> = {
  sm: 'gap-0.5',
  md: 'gap-1',
  lg: 'gap-1.5',
};

export const toggleItemBaseStyles = [
  'inline-flex items-center justify-center rounded-md',
  'text-sm font-medium transition-all duration-200 ease-in-out',
  'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
  'select-none cursor-pointer',
  'border border-[#EBEBEB] hover:border-[#D1D1D1]',
  'data-[state=on]:border-[#272727] data-[state=on]:hover:border-[#000000]',
].join(' ');

export const toggleItemFullWidthStyles = 'flex-1';

export const toggleItemSizeStyles: Record<ToggleSize, string> = {
  sm: 'text-xs h-8 px-2.5 gap-1',
  md: 'text-sm h-9 px-3 gap-1.5',
  lg: 'text-base h-10 px-4 gap-2',
};
