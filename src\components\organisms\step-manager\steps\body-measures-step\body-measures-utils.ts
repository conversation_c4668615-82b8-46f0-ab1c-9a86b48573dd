import { UseFormSetValue } from 'react-hook-form';
import { SIZE_GUIDE_KEYS } from '@/constants/pocket-virtual-fitting-room';
import { PocketVFRFormData } from '@/types/pocket-virtual-fitting-room';
import { convertCmToFtIn, convertFtInToCm, convertKgToLb, convertLbToKg } from '@/utils/measurement-conversions';

export interface WeightUnitySystem {
  setValue: UseFormSetValue<PocketVFRFormData>;
  isPounds: boolean;
  weight: string;
}
export interface HeightUnitySystem {
  setValue: UseFormSetValue<PocketVFRFormData>;
  isMetric: boolean;
  fields: {
    height?: string;
    heightIn?: string;
    heightFt?: string;
  };
}

export function syncHeightUnits({ setValue, isMetric, fields }: HeightUnitySystem) {
  const { height, heightIn, heightFt } = fields;

  if ((!heightFt && isMetric) || (!height && !isMetric)) return;

  const heighVariation = {
    cm: height ? Number(height) : 0,
    ft: heightFt ? Number(heightFt) : 0,
    inches: heightIn ? Number(heightIn) : 0,
  };

  let twoWaySinc = isMetric;
  for (let unitySystems = 0; unitySystems < Object.entries(heighVariation).length; unitySystems++) {
    if (twoWaySinc) {
      heighVariation.cm = convertFtInToCm(heighVariation.ft, heighVariation.inches);
    } else {
      const { ft, inches } = convertCmToFtIn(heighVariation.cm);
      heighVariation.ft = ft;
      heighVariation.inches = inches;
    }
    twoWaySinc = !twoWaySinc;
  }
  setValue(SIZE_GUIDE_KEYS.height, heighVariation.cm.toString());
  setValue(SIZE_GUIDE_KEYS.heightFt, heighVariation.ft.toString());
  setValue(SIZE_GUIDE_KEYS.heightIn, heighVariation.inches.toString());
}

export function syncWeightUnits({ setValue, isPounds, weight }: WeightUnitySystem) {
  if (!weight) return;
  const newWeight = isPounds ? convertLbToKg(Number(weight)).toString() : convertKgToLb(Number(weight)).toString();
  setValue(SIZE_GUIDE_KEYS.weight, newWeight);
}
