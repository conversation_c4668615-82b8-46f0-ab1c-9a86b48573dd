import { ButtonProps } from './button.types';
import { cn } from '@/utils/class-utils';

const buttonStyles = {
  base: `
    rounded-lg inline-flex items-center justify-center gap-3
    transition-colors duration-200 font-medium
    enabled:cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed
    focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2
  `.trim(),
  variants: {
    primary: 'border bg-[#272727] text-white enabled:hover:bg-[#272727]/90',
    secondary: 'border bg-[#DFDFDF] text-[#262626] enabled:hover:bg-[#272727]/10 enabled:touch-bg-[#272727]/10',
    classic:
      'border border-[#D6D6D6] bg-[#FFFFFF] text-[#272727] enabled:hover:bg-[#272727]/10 enabled:touch-bg-[#272727]/10',
    blank: '',
  },
  sizes: {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg',
  },
};

export function Button({
  variant = 'primary',
  size = 'md',
  fullWidth,
  className,
  children,
  isLoading,
  showSpinner = true,
  disabled,
  ...props
}: ButtonProps) {
  return (
    <button
      className={cn(
        buttonStyles.base,
        buttonStyles.variants[variant],
        buttonStyles.sizes[size],
        isLoading && 'pointer-events-none opacity-70',
        fullWidth && 'w-full',
        className
      )}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading && showSpinner && (
        <svg
          className="animate-spin -ml-1 mr-3 h-5 w-5"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      {children}
    </button>
  );
}
